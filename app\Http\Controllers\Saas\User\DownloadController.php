<?php

namespace App\Http\Controllers\Saas\User;

use App\Http\Controllers\Controller;
use App\Http\Services\Saas\DownloadService;
use App\Models\Package;
use App\Models\PackageDownload;
use App\Models\User;
use App\Traits\ResponseTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;

class DownloadController extends Controller
{
    use ResponseTrait;

    protected $downloadService;

    public function __construct()
    {
        $this->downloadService = new DownloadService();
    }

    /**
     * Afficher la liste des téléchargements de l'utilisateur
     */
    public function index(Request $request)
    {
        $userId = auth()->id();
        
        // Récupérer les statistiques
        $data['totalDownloads'] = PackageDownload::where('user_id', $userId)->count();
        $data['activeDownloads'] = PackageDownload::where('user_id', $userId)
            ->where('status', true)
            ->where('expires_at', '>', Carbon::now())
            ->count();
        $data['recentDownloads'] = PackageDownload::where('user_id', $userId)
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->count();
        $data['availablePackages'] = Package::where('status', STATUS_ACTIVE)->count();
        
        // Récupérer les packages pour les filtres
        $data['packages'] = Package::where('status', STATUS_ACTIVE)->get();
        
        // Appliquer les filtres
        $query = PackageDownload::with(['package'])
            ->where('user_id', $userId);
        
        if ($request->package_id) {
            $query->where('package_id', $request->package_id);
        }
        
        if ($request->status !== null && $request->status !== '') {
            if ($request->status == 1) {
                $query->where('status', true)->where('expires_at', '>', Carbon::now());
            } else {
                $query->where(function($q) {
                    $q->where('status', false)
                      ->orWhere('expires_at', '<=', Carbon::now());
                });
            }
        }
        
        if ($request->date) {
            $query->whereDate('created_at', $request->date);
        }
        
        $data['downloads'] = $query->orderBy('created_at', 'desc')->paginate(15);
        
        return view('saas.user.downloads.index', $data);
    }

    /**
     * Afficher les détails d'un téléchargement
     */
    public function show($id)
    {
        $download = PackageDownload::with(['package'])
            ->where('user_id', auth()->id())
            ->findOrFail($id);
        
        return view('saas.user.downloads.show', compact('download'));
    }

    /**
     * Télécharger un package
     */
    public function download($id)
    {
        try {
            $download = PackageDownload::with(['package', 'user'])
                ->where('user_id', auth()->id())
                ->findOrFail($id);
            
            // Vérifier si le téléchargement est actif
            if (!$download->isActive()) {
                return $this->error([], __('This download is no longer available'));
            }
            
            // Vérifier les permissions
            if (!$this->downloadService->canDownload($download)) {
                return $this->error([], __('You do not have permission to download this package'));
            }
            
            // Générer le lien de téléchargement
            $downloadLink = $this->downloadService->generateDownloadLink($download);
            
            // Incrémenter le compteur
            $download->incrementDownloadCount();
            
            return $this->success([
                'download_url' => $downloadLink,
                'package_name' => $download->package->name,
                'expires_at' => $download->expires_at->format('Y-m-d H:i:s')
            ], __('Download link generated successfully'));
            
        } catch (\Exception $e) {
            \Log::error('Error downloading package: ' . $e->getMessage());
            return $this->error([], __('Error processing download'));
        }
    }

    /**
     * Demander un renouvellement
     */
    public function requestRenewal($id)
    {
        try {
            $download = PackageDownload::with(['package'])
                ->where('user_id', auth()->id())
                ->findOrFail($id);
            
            // Vérifier si le téléchargement est expiré
            if (!$download->isExpired()) {
                return $this->error([], __('This download is still active'));
            }
            
            // Créer une nouvelle demande de renouvellement
            $renewal = PackageDownload::create([
                'user_id' => auth()->id(),
                'package_id' => $download->package_id,
                'subscription_id' => $download->subscription_id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'status' => false, // En attente d'approbation
                'download_count' => 0,
                'download_token' => \Str::random(64),
                'expires_at' => Carbon::now()->addDays(7),
                'is_completed' => false
            ]);
            
            // Envoyer notification à l'admin
            $this->sendRenewalNotification($renewal);
            
            return $this->success([], __('Renewal request submitted successfully. You will be notified once approved.'));
            
        } catch (\Exception $e) {
            \Log::error('Error requesting renewal: ' . $e->getMessage());
            return $this->error([], __('Error submitting renewal request'));
        }
    }

    /**
     * Obtenir les statistiques de l'utilisateur
     */
    public function statistics()
    {
        $userId = auth()->id();
        
        $data = [
            'total_downloads' => PackageDownload::where('user_id', $userId)->count(),
            'active_downloads' => PackageDownload::where('user_id', $userId)
                ->where('status', true)
                ->where('expires_at', '>', Carbon::now())
                ->count(),
            'expired_downloads' => PackageDownload::where('user_id', $userId)
                ->where(function($q) {
                    $q->where('status', false)
                      ->orWhere('expires_at', '<=', Carbon::now());
                })->count(),
            'recent_activity' => PackageDownload::where('user_id', $userId)
                ->with('package')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get(),
            'downloads_by_package' => PackageDownload::selectRaw('package_id, COUNT(*) as download_count')
                ->where('user_id', $userId)
                ->with('package')
                ->groupBy('package_id')
                ->orderBy('download_count', 'desc')
                ->limit(5)
                ->get()
        ];
        
        return $this->success($data);
    }

    /**
     * Téléchargements par package
     */
    public function packageDownloads($packageId)
    {
        $userId = auth()->id();
        
        // Vérifier que l'utilisateur a accès à ce package
        $hasAccess = PackageDownload::where('user_id', $userId)
            ->where('package_id', $packageId)
            ->where('status', true)
            ->where('expires_at', '>', Carbon::now())
            ->exists();
        
        if (!$hasAccess) {
            return redirect()->route('user.downloads.index')
                ->with('error', __('You do not have access to this package'));
        }
        
        $package = Package::findOrFail($packageId);
        $downloads = PackageDownload::where('user_id', $userId)
            ->where('package_id', $packageId)
            ->orderBy('created_at', 'desc')
            ->paginate(15);
        
        return view('saas.user.downloads.package', compact('package', 'downloads'));
    }

    /**
     * Historique des téléchargements
     */
    public function history()
    {
        $userId = auth()->id();
        
        $downloads = PackageDownload::where('user_id', $userId)
            ->with(['package'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);
        
        return view('saas.user.downloads.history', compact('downloads'));
    }

    /**
     * Téléchargements expirés
     */
    public function expired()
    {
        $userId = auth()->id();
        
        $downloads = PackageDownload::where('user_id', $userId)
            ->with(['package'])
            ->where(function($query) {
                $query->where('status', false)
                      ->orWhere('expires_at', '<=', Carbon::now());
            })
            ->orderBy('expires_at', 'desc')
            ->paginate(15);
        
        return view('saas.user.downloads.expired', compact('downloads'));
    }

    /**
     * Rechercher des téléchargements
     */
    public function search(Request $request)
    {
        $userId = auth()->id();
        $query = $request->get('q');
        
        $downloads = PackageDownload::where('user_id', $userId)
            ->with(['package'])
            ->whereHas('package', function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%");
            })
            ->orderBy('created_at', 'desc')
            ->paginate(15);
        
        return view('saas.user.downloads.search', compact('downloads', 'query'));
    }

    /**
     * Envoyer notification de renouvellement à l'admin
     */
    private function sendRenewalNotification($renewal)
    {
        try {
            // Logique pour envoyer une notification à l'admin
            // Cela peut être un email, une notification en base de données, etc.
            
            \Log::info("Renewal request submitted", [
                'user_id' => $renewal->user_id,
                'package_id' => $renewal->package_id,
                'download_id' => $renewal->id
            ]);
            
            return true;
        } catch (\Exception $e) {
            \Log::error('Error sending renewal notification: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Vérifier les permissions de téléchargement
     */
    private function checkDownloadPermissions($download)
    {
        $user = auth()->user();
        
        // Vérifier si l'utilisateur a un abonnement actif
        $activeSubscription = $user->subscriptions()
            ->where('package_id', $download->package_id)
            ->where('status', STATUS_ACTIVE)
            ->where('expired_at', '>', Carbon::now())
            ->first();
        
        if (!$activeSubscription) {
            return false;
        }
        
        // Vérifier les limites de téléchargement
        $package = $download->package;
        
        if ($package->download_limit_type == 2) { // Illimité
            return true;
        }
        
        $downloadCount = PackageDownload::where('user_id', $user->id)
            ->where('package_id', $package->id)
            ->sum('download_count');
        
        return $downloadCount < $package->download_limit;
    }
} 
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\ApiKey;
use App\Models\ApiKeyLog;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ApiKeyMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string|null  $permission
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $permission = null)
    {
        $startTime = microtime(true);
        
        // Récupérer la clé API depuis l'en-tête Authorization
        $authHeader = $request->header('Authorization');
        
        if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
            return $this->unauthorizedResponse('Missing or invalid Authorization header');
        }
        
        $apiKey = substr($authHeader, 7); // Enlever "Bearer "
        
        // Vérifier si la clé API existe et est active
        $keyModel = ApiKey::where('key', $apiKey)->where('is_active', true)->first();
        
        if (!$keyModel) {
            return $this->unauthorizedResponse('Invalid or inactive API key');
        }
        
        // Vérifier les permissions si spécifiées
        if ($permission && !$keyModel->hasPermission($permission)) {
            return $this->unauthorizedResponse("Insufficient permissions. Required: {$permission}");
        }
        
        // Marquer la clé comme utilisée
        $keyModel->markAsUsed();
        
        // Ajouter la clé API au request pour utilisation dans les contrôleurs
        $request->merge(['api_key' => $keyModel]);
        
        // Continuer avec la requête
        $response = $next($request);
        
        // Calculer la durée de la requête
        $duration = round((microtime(true) - $startTime) * 1000);
        
        // Logger la requête
        $this->logApiRequest($keyModel, $request, $response, $duration);
        
        return $response;
    }
    
    /**
     * Réponse d'erreur d'authentification
     */
    private function unauthorizedResponse($message)
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'error_code' => 'UNAUTHORIZED'
        ], 401);
    }
    
    /**
     * Logger la requête API
     */
    private function logApiRequest($apiKey, Request $request, $response, $duration)
    {
        try {
            $requestData = $this->sanitizeRequestData($request->all());
            $responseData = $this->sanitizeResponseData($response->getContent());
            
            ApiKeyLog::create([
                'api_key_id' => $apiKey->id,
                'endpoint' => $request->path(),
                'method' => $request->method(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'request_data' => $requestData,
                'response_data' => $responseData,
                'status' => $response->getStatusCode() < 400 ? 'success' : 'error',
                'response_code' => $response->getStatusCode(),
                'gateway' => $this->extractGateway($request),
                'duration' => $duration,
                'error_message' => $response->getStatusCode() >= 400 ? $responseData['message'] ?? 'Error' : null
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to log API request', [
                'api_key_id' => $apiKey->id,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Nettoyer les données de requête sensibles
     */
    private function sanitizeRequestData($data)
    {
        $sensitiveFields = ['password', 'token', 'secret', 'key', 'api_key'];
        
        return $this->removeSensitiveData($data, $sensitiveFields);
    }
    
    /**
     * Nettoyer les données de réponse sensibles
     */
    private function sanitizeResponseData($content)
    {
        try {
            $data = json_decode($content, true);
            
            if (is_array($data)) {
                $sensitiveFields = ['password', 'token', 'secret', 'key', 'api_key'];
                return $this->removeSensitiveData($data, $sensitiveFields);
            }
            
            return $data;
        } catch (\Exception $e) {
            return ['content' => 'Unable to parse response'];
        }
    }
    
    /**
     * Supprimer les données sensibles
     */
    private function removeSensitiveData($data, $sensitiveFields)
    {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                if (in_array(strtolower($key), $sensitiveFields)) {
                    $data[$key] = '***HIDDEN***';
                } elseif (is_array($value)) {
                    $data[$key] = $this->removeSensitiveData($value, $sensitiveFields);
                }
            }
        }
        
        return $data;
    }
    
    /**
     * Extraire le gateway depuis la requête
     */
    private function extractGateway(Request $request)
    {
        $path = $request->path();
        
        if (str_contains($path, 'webhooks/')) {
            $parts = explode('/', $path);
            return end($parts);
        }
        
        return null;
    }
} 
<?php

namespace App\Http\Controllers\Saas\Admin;

use App\Http\Controllers\Controller;
use App\Http\Services\Saas\DownloadService;
use App\Models\Package;
use App\Models\User;
use App\Models\PackageDownload;
use App\Traits\ResponseTrait;
use Illuminate\Http\Request;
use Carbon\Carbon;

class DownloadController extends Controller
{
    use ResponseTrait;

    protected $downloadService;

    public function __construct()
    {
        $this->downloadService = new DownloadService();
    }

    /**
     * Afficher la liste des téléchargements
     */
    public function index(Request $request)
    {
        $data['title'] = __('Downloads Management');
        $data['activeDownloads'] = 'active';
        
        // Récupérer les statistiques
        $data['totalDownloads'] = PackageDownload::count();
        $data['activeDownloads'] = PackageDownload::where('status', STATUS_ACTIVE)->count();
        $data['uniqueUsers'] = PackageDownload::distinct('user_id')->count();
        $data['todayDownloads'] = PackageDownload::whereDate('created_at', Carbon::today())->count();
        
        // Récupérer les données pour les filtres
        $data['packages'] = Package::where('status', STATUS_ACTIVE)->get();
        $data['users'] = User::where('role', USER_ROLE_USER)->get();
        
        // Appliquer les filtres
        $query = PackageDownload::with(['user', 'package']);
        
        if ($request->package_id) {
            $query->where('package_id', $request->package_id);
        }
        
        if ($request->user_id) {
            $query->where('user_id', $request->user_id);
        }
        
        if ($request->date) {
            $query->whereDate('created_at', $request->date);
        }
        
        if ($request->status !== null && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        $data['downloads'] = $query->orderBy('created_at', 'desc')->paginate(25);
        
        return view('saas.admin.downloads.index', $data);
    }

    /**
     * Afficher les détails d'un téléchargement
     */
    public function show($id)
    {
        $download = PackageDownload::with(['user', 'package'])->findOrFail($id);
        
        return view('saas.admin.downloads.show', compact('download'));
    }

    /**
     * Renvoyer un téléchargement
     */
    public function resend($id)
    {
        try {
            $download = PackageDownload::findOrFail($id);
            
            // Logique pour renvoyer le téléchargement
            $download->status = STATUS_ACTIVE;
            $download->resend_count = ($download->resend_count ?? 0) + 1;
            $download->last_resend_at = Carbon::now();
            $download->save();
            
            // Envoyer l'email de notification
            $this->downloadService->sendDownloadNotification($download);
            
            return $this->success([], __('Download resent successfully'));
        } catch (\Exception $e) {
            return $this->error([], __('Error resending download'));
        }
    }

    /**
     * Supprimer un téléchargement
     */
    public function destroy($id)
    {
        try {
            $download = PackageDownload::findOrFail($id);
            $download->delete();
            
            return $this->success([], __('Download record deleted successfully'));
        } catch (\Exception $e) {
            return $this->error([], __('Error deleting download record'));
        }
    }

    /**
     * Exporter les téléchargements
     */
    public function export(Request $request)
    {
        $query = PackageDownload::with(['user', 'package']);
        
        // Appliquer les mêmes filtres que dans index()
        if ($request->package_id) {
            $query->where('package_id', $request->package_id);
        }
        
        if ($request->user_id) {
            $query->where('user_id', $request->user_id);
        }
        
        if ($request->date) {
            $query->whereDate('created_at', $request->date);
        }
        
        if ($request->status !== null && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        $downloads = $query->orderBy('created_at', 'desc')->get();
        
        return $this->downloadService->exportToExcel($downloads);
    }

    /**
     * Statistiques des téléchargements
     */
    public function statistics()
    {
        $data = [
            'total_downloads' => PackageDownload::count(),
            'active_downloads' => PackageDownload::where('status', STATUS_ACTIVE)->count(),
            'unique_users' => PackageDownload::distinct('user_id')->count(),
            'today_downloads' => PackageDownload::whereDate('created_at', Carbon::today())->count(),
            'weekly_downloads' => PackageDownload::whereBetween('created_at', [
                Carbon::now()->startOfWeek(),
                Carbon::now()->endOfWeek()
            ])->count(),
            'monthly_downloads' => PackageDownload::whereMonth('created_at', Carbon::now()->month)->count(),
            'top_packages' => PackageDownload::selectRaw('package_id, COUNT(*) as download_count')
                ->with('package')
                ->groupBy('package_id')
                ->orderBy('download_count', 'desc')
                ->limit(5)
                ->get(),
            'downloads_by_date' => PackageDownload::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->whereBetween('created_at', [
                    Carbon::now()->subDays(30),
                    Carbon::now()
                ])
                ->groupBy('date')
                ->orderBy('date')
                ->get()
        ];
        
        return $this->success($data);
    }

    /**
     * Téléchargements par package
     */
    public function packageDownloads($packageId)
    {
        $package = Package::findOrFail($packageId);
        $downloads = PackageDownload::where('package_id', $packageId)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(25);
        
        return view('saas.admin.downloads.package', compact('package', 'downloads'));
    }

    /**
     * Téléchargements par utilisateur
     */
    public function userDownloads($userId)
    {
        $user = User::findOrFail($userId);
        $downloads = PackageDownload::where('user_id', $userId)
            ->with('package')
            ->orderBy('created_at', 'desc')
            ->paginate(25);
        
        return view('saas.admin.downloads.user', compact('user', 'downloads'));
    }

    /**
     * Marquer un téléchargement comme actif/inactif
     */
    public function toggleStatus($id)
    {
        try {
            $download = PackageDownload::findOrFail($id);
            $download->status = $download->status ? 0 : 1;
            $download->save();
            
            return $this->success([
                'status' => $download->status
            ], __('Download status updated successfully'));
        } catch (\Exception $e) {
            return $this->error([], __('Error updating download status'));
        }
    }

    /**
     * Bulk actions pour les téléchargements
     */
    public function bulkAction(Request $request)
    {
        $action = $request->action;
        $ids = $request->ids;
        
        if (!$ids || !$action) {
            return $this->error([], __('Please select items and action'));
        }
        
        try {
            switch ($action) {
                case 'delete':
                    PackageDownload::whereIn('id', $ids)->delete();
                    $message = __('Selected downloads deleted successfully');
                    break;
                    
                case 'activate':
                    PackageDownload::whereIn('id', $ids)->update(['status' => STATUS_ACTIVE]);
                    $message = __('Selected downloads activated successfully');
                    break;
                    
                case 'deactivate':
                    PackageDownload::whereIn('id', $ids)->update(['status' => 0]);
                    $message = __('Selected downloads deactivated successfully');
                    break;
                    
                case 'resend':
                    $downloads = PackageDownload::whereIn('id', $ids)->get();
                    foreach ($downloads as $download) {
                        $this->downloadService->sendDownloadNotification($download);
                    }
                    $message = __('Selected downloads resent successfully');
                    break;
                    
                default:
                    return $this->error([], __('Invalid action'));
            }
            
            return $this->success([], $message);
        } catch (\Exception $e) {
            return $this->error([], __('Error performing bulk action'));
        }
    }
} 
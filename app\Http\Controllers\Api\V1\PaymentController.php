<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Order;
use App\Models\Customer;
use App\Models\Subscription;
use App\Models\DigitalDownload;
use App\Services\PaymentService;
use App\Services\DigitalDeliveryService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

class PaymentController extends Controller
{
    protected $paymentService;
    protected $digitalDeliveryService;

    public function __construct(PaymentService $paymentService, DigitalDeliveryService $digitalDeliveryService)
    {
        $this->paymentService = $paymentService;
        $this->digitalDeliveryService = $digitalDeliveryService;
    }

    /**
     * Créer un paiement
     * POST /api/v1/payments
     */
    public function createPayment(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_id' => 'required|exists:products,id',
                'customer_email' => 'required|email',
                'customer_name' => 'required|string|max:255',
                'amount' => 'required|numeric|min:0.01',
                'currency' => 'required|string|size:3',
                'gateway' => 'required|string|in:stripe,paypal,razorpay,mollie',
                'return_url' => 'required|url',
                'cancel_url' => 'required|url',
                'webhook_url' => 'nullable|url',
                'metadata' => 'nullable|array',
                'subscription' => 'nullable|boolean',
                'subscription_interval' => 'nullable|string|in:monthly,yearly',
                'subscription_trial_days' => 'nullable|integer|min:0',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $product = Product::findOrFail($request->product_id);
            
            // Créer ou récupérer le client
            $customer = Customer::firstOrCreate(
                ['email' => $request->customer_email],
                [
                    'name' => $request->customer_name,
                    'user_id' => $product->user_id,
                    'status' => true
                ]
            );

            // Créer la commande
            $order = Order::create([
                'user_id' => $product->user_id,
                'customer_id' => $customer->id,
                'product_id' => $product->id,
                'amount' => $request->amount,
                'currency' => $request->currency,
                'gateway' => $request->gateway,
                'status' => 'pending',
                'return_url' => $request->return_url,
                'cancel_url' => $request->cancel_url,
                'webhook_url' => $request->webhook_url,
                'metadata' => $request->metadata ?? [],
                'external_order_id' => 'API_' . Str::random(16),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            // Créer le paiement via le service
            $paymentData = [
                'order_id' => $order->id,
                'amount' => $request->amount,
                'currency' => $request->currency,
                'gateway' => $request->gateway,
                'customer' => $customer,
                'product' => $product,
                'return_url' => $request->return_url,
                'cancel_url' => $request->cancel_url,
                'metadata' => $request->metadata ?? [],
                'subscription' => $request->subscription ?? false,
                'subscription_interval' => $request->subscription_interval,
                'subscription_trial_days' => $request->subscription_trial_days
            ];

            $paymentResult = $this->paymentService->createPayment($paymentData);

            if (!$paymentResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $paymentResult['message']
                ], 400);
            }

            // Log de l'activité
            Log::info('API Payment created', [
                'order_id' => $order->id,
                'product_id' => $product->id,
                'customer_id' => $customer->id,
                'amount' => $request->amount,
                'gateway' => $request->gateway
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment created successfully',
                'data' => [
                    'order_id' => $order->id,
                    'payment_id' => $paymentResult['payment_id'],
                    'checkout_url' => $paymentResult['checkout_url'],
                    'payment_intent' => $paymentResult['payment_intent'] ?? null,
                    'expires_at' => $paymentResult['expires_at'] ?? null
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('API Payment creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'PAYMENT_CREATION_FAILED'
            ], 500);
        }
    }

    /**
     * Vérifier le statut d'un paiement
     * GET /api/v1/payments/{order_id}/status
     */
    public function getPaymentStatus(string $orderId): JsonResponse
    {
        try {
            $order = Order::where('external_order_id', $orderId)
                         ->orWhere('id', $orderId)
                         ->with(['customer', 'product'])
                         ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found'
                ], 404);
            }

            $statusData = [
                'order_id' => $order->id,
                'external_order_id' => $order->external_order_id,
                'status' => $order->status,
                'amount' => $order->amount,
                'currency' => $order->currency,
                'gateway' => $order->gateway,
                'created_at' => $order->created_at,
                'updated_at' => $order->updated_at,
                'customer' => [
                    'id' => $order->customer->id,
                    'name' => $order->customer->name,
                    'email' => $order->customer->email
                ],
                'product' => [
                    'id' => $order->product->id,
                    'name' => $order->product->name,
                    'is_digital' => $order->product->is_digital
                ]
            ];

            // Ajouter les informations de livraison digitale si applicable
            if ($order->product->is_digital && $order->status === 'completed') {
                $digitalDownload = DigitalDownload::where('order_id', $order->id)->first();
                if ($digitalDownload) {
                    $statusData['digital_delivery'] = [
                        'download_token' => $digitalDownload->download_token,
                        'download_url' => route('api.v1.downloads.download', $digitalDownload->download_token),
                        'expires_at' => $digitalDownload->expires_at,
                        'download_count' => $digitalDownload->download_count
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'data' => $statusData
            ]);

        } catch (\Exception $e) {
            Log::error('API Payment status check failed', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'STATUS_CHECK_FAILED'
            ], 500);
        }
    }

    /**
     * Annuler un paiement
     * POST /api/v1/payments/{order_id}/cancel
     */
    public function cancelPayment(string $orderId): JsonResponse
    {
        try {
            $order = Order::where('external_order_id', $orderId)
                         ->orWhere('id', $orderId)
                         ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found'
                ], 404);
            }

            if ($order->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Order cannot be cancelled',
                    'current_status' => $order->status
                ], 400);
            }

            $cancelResult = $this->paymentService->cancelPayment($order);

            if (!$cancelResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $cancelResult['message']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Payment cancelled successfully',
                'data' => [
                    'order_id' => $order->id,
                    'status' => $order->status
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('API Payment cancellation failed', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'CANCELLATION_FAILED'
            ], 500);
        }
    }

    /**
     * Rembourser un paiement
     * POST /api/v1/payments/{order_id}/refund
     */
    public function refundPayment(Request $request, string $orderId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'amount' => 'nullable|numeric|min:0.01',
                'reason' => 'nullable|string|max:255'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $order = Order::where('external_order_id', $orderId)
                         ->orWhere('id', $orderId)
                         ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found'
                ], 404);
            }

            if ($order->status !== 'completed') {
                return response()->json([
                    'success' => false,
                    'message' => 'Order cannot be refunded',
                    'current_status' => $order->status
                ], 400);
            }

            $refundAmount = $request->amount ?? $order->amount;
            $refundResult = $this->paymentService->refundPayment($order, $refundAmount, $request->reason);

            if (!$refundResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $refundResult['message']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Payment refunded successfully',
                'data' => [
                    'order_id' => $order->id,
                    'refund_amount' => $refundAmount,
                    'refund_id' => $refundResult['refund_id'] ?? null
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('API Payment refund failed', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'REFUND_FAILED'
            ], 500);
        }
    }

    /**
     * Webhook pour les notifications de paiement
     * POST /api/v1/webhooks/{gateway}
     */
    public function webhook(Request $request, string $gateway): JsonResponse
    {
        try {
            $webhookResult = $this->paymentService->handleWebhook($request, $gateway);

            if (!$webhookResult['success']) {
                Log::warning('Webhook processing failed', [
                    'gateway' => $gateway,
                    'error' => $webhookResult['message']
                ]);

                return response()->json([
                    'success' => false,
                    'message' => $webhookResult['message']
                ], 400);
            }

            // Traitement de la livraison digitale si applicable
            if ($webhookResult['order'] && $webhookResult['order']->product->is_digital) {
                $this->digitalDeliveryService->processDigitalDelivery($webhookResult['order']);
            }

            return response()->json([
                'success' => true,
                'message' => 'Webhook processed successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Webhook processing failed', [
                'gateway' => $gateway,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Obtenir les statistiques de paiement
     * GET /api/v1/payments/stats
     */
    public function getPaymentStats(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_id' => 'required|exists:users,id',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after:start_date',
                'gateway' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $query = Order::where('user_id', $request->user_id);

            if ($request->start_date) {
                $query->where('created_at', '>=', $request->start_date);
            }

            if ($request->end_date) {
                $query->where('created_at', '<=', $request->end_date);
            }

            if ($request->gateway) {
                $query->where('gateway', $request->gateway);
            }

            $stats = [
                'total_orders' => $query->count(),
                'completed_orders' => $query->where('status', 'completed')->count(),
                'pending_orders' => $query->where('status', 'pending')->count(),
                'failed_orders' => $query->where('status', 'failed')->count(),
                'total_revenue' => $query->where('status', 'completed')->sum('amount'),
                'average_order_value' => $query->where('status', 'completed')->avg('amount'),
                'gateway_breakdown' => $query->selectRaw('gateway, COUNT(*) as count, SUM(amount) as total')
                                            ->where('status', 'completed')
                                            ->groupBy('gateway')
                                            ->get()
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error('API Payment stats failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'STATS_FAILED'
            ], 500);
        }
    }
} 
<?php

namespace App\Http\Requests;

use App\Rules\ReCaptcha;
use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        if(getOption('google_recaptcha_status') == 1){
            return [
                'email' => 'required',
                'password' => 'required|string',
                'g-recaptcha-response' => ['required', new ReCaptcha]
            ];
        }else{
            return [
                'email' => 'required',
                'password' => 'required|string',
            ];
        }
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'email.required' => 'The email field is required.',
        ];
    }
}

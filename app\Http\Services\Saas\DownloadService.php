<?php

namespace App\Http\Services\Saas;

use App\Models\PackageDownload;
use App\Models\Package;
use App\Models\User;
use App\Mail\DownloadNotification;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class DownloadService
{
    /**
     * Enregistrer un nouveau téléchargement
     */
    public function recordDownload($userId, $packageId, $request)
    {
        try {
            $download = PackageDownload::create([
                'user_id' => $userId,
                'package_id' => $packageId,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'status' => STATUS_ACTIVE,
                'download_count' => 1,
                'last_download_at' => Carbon::now(),
            ]);

            // Envoyer notification par email
            $this->sendDownloadNotification($download);

            return $download;
        } catch (\Exception $e) {
            \Log::error('Error recording download: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Envoyer notification de téléchargement
     */
    public function sendDownloadNotification($download)
    {
        try {
            $user = $download->user;
            $package = $download->package;

            // Envoyer email de notification
            Mail::to($user->email)->send(new DownloadNotification($download));

            // Mettre à jour le compteur de téléchargements
            $download->increment('notification_count');
            $download->last_notification_at = Carbon::now();
            $download->save();

            return true;
        } catch (\Exception $e) {
            \Log::error('Error sending download notification: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Générer le lien de téléchargement sécurisé
     */
    public function generateDownloadLink($download)
    {
        $token = \Str::random(64);
        $expiresAt = Carbon::now()->addHours(24);

        // Stocker le token temporairement
        \Cache::put("download_token_{$token}", $download->id, $expiresAt);

        return route('download.package', [
            'token' => $token,
            'package' => $download->package->id
        ]);
    }

    /**
     * Vérifier et traiter un téléchargement
     */
    public function processDownload($token, $packageId)
    {
        try {
            // Vérifier le token
            $downloadId = \Cache::get("download_token_{$token}");
            if (!$downloadId) {
                return ['success' => false, 'message' => __('Invalid or expired download link')];
            }

            $download = PackageDownload::with(['user', 'package'])->find($downloadId);
            if (!$download || $download->package_id != $packageId) {
                return ['success' => false, 'message' => __('Download not found')];
            }

            // Vérifier les permissions
            if (!$this->canDownload($download)) {
                return ['success' => false, 'message' => __('Download access denied')];
            }

            // Mettre à jour les statistiques
            $download->increment('download_count');
            $download->last_download_at = Carbon::now();
            $download->save();

            // Supprimer le token utilisé
            \Cache::forget("download_token_{$token}");

            return [
                'success' => true,
                'download' => $download,
                'file_path' => $this->getPackageFilePath($download->package)
            ];

        } catch (\Exception $e) {
            \Log::error('Error processing download: ' . $e->getMessage());
            return ['success' => false, 'message' => __('Error processing download')];
        }
    }

    /**
     * Vérifier si l'utilisateur peut télécharger
     */
    public function canDownload($download)
    {
        $user = $download->user;
        $package = $download->package;

        // Vérifier si l'utilisateur a un abonnement actif
        $activeSubscription = $user->subscriptions()
            ->where('package_id', $package->id)
            ->where('status', STATUS_ACTIVE)
            ->where('expired_at', '>', Carbon::now())
            ->first();

        if (!$activeSubscription) {
            return false;
        }

        // Vérifier les limites de téléchargement
        if ($package->download_limit_type == 2) { // Illimité
            return true;
        }

        $downloadCount = PackageDownload::where('user_id', $user->id)
            ->where('package_id', $package->id)
            ->sum('download_count');

        return $downloadCount < $package->download_limit;
    }

    /**
     * Obtenir le chemin du fichier du package
     */
    public function getPackageFilePath($package)
    {
        // Logique pour récupérer le fichier du package
        // Cela peut être un fichier physique ou un lien vers un service externe
        $filePath = $package->file_path ?? null;

        if (!$filePath) {
            throw new \Exception(__('Package file not found'));
        }

        // Vérifier si le fichier existe
        if (!Storage::exists($filePath)) {
            throw new \Exception(__('Package file not available'));
        }

        return $filePath;
    }

    /**
     * Exporter les téléchargements vers Excel
     */
    public function exportToExcel($downloads)
    {
        try {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // En-têtes
            $headers = [
                'ID', 'User', 'Email', 'Package', 'Download Date', 
                'IP Address', 'User Agent', 'Status', 'Download Count'
            ];

            foreach ($headers as $index => $header) {
                $sheet->setCellValueByColumnAndRow($index + 1, 1, $header);
            }

            // Données
            $row = 2;
            foreach ($downloads as $download) {
                $sheet->setCellValueByColumnAndRow(1, $row, $download->id);
                $sheet->setCellValueByColumnAndRow(2, $row, $download->user->name ?? 'N/A');
                $sheet->setCellValueByColumnAndRow(3, $row, $download->user->email ?? 'N/A');
                $sheet->setCellValueByColumnAndRow(4, $row, $download->package->name ?? 'N/A');
                $sheet->setCellValueByColumnAndRow(5, $row, $download->created_at->format('Y-m-d H:i:s'));
                $sheet->setCellValueByColumnAndRow(6, $row, $download->ip_address);
                $sheet->setCellValueByColumnAndRow(7, $row, $download->user_agent);
                $sheet->setCellValueByColumnAndRow(8, $row, $download->status ? 'Active' : 'Inactive');
                $sheet->setCellValueByColumnAndRow(9, $row, $download->download_count);
                $row++;
            }

            // Auto-dimensionner les colonnes
            foreach (range(1, count($headers)) as $column) {
                $sheet->getColumnDimensionByColumn($column)->setAutoSize(true);
            }

            // Créer le fichier
            $writer = new Xlsx($spreadsheet);
            $filename = 'downloads_' . Carbon::now()->format('Y-m-d_H-i-s') . '.xlsx';
            $filepath = storage_path('app/temp/' . $filename);

            // Créer le dossier temp s'il n'existe pas
            if (!file_exists(storage_path('app/temp'))) {
                mkdir(storage_path('app/temp'), 0755, true);
            }

            $writer->save($filepath);

            return response()->download($filepath, $filename)->deleteFileAfterSend();

        } catch (\Exception $e) {
            \Log::error('Error exporting downloads: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtenir les statistiques des téléchargements
     */
    public function getStatistics()
    {
        $now = Carbon::now();
        $startOfMonth = $now->startOfMonth();
        $startOfWeek = $now->startOfWeek();

        return [
            'total_downloads' => PackageDownload::count(),
            'active_downloads' => PackageDownload::where('status', STATUS_ACTIVE)->count(),
            'unique_users' => PackageDownload::distinct('user_id')->count(),
            'today_downloads' => PackageDownload::whereDate('created_at', $now->toDateString())->count(),
            'weekly_downloads' => PackageDownload::whereBetween('created_at', [$startOfWeek, $now])->count(),
            'monthly_downloads' => PackageDownload::whereBetween('created_at', [$startOfMonth, $now])->count(),
            'top_packages' => $this->getTopPackages(),
            'downloads_trend' => $this->getDownloadsTrend(),
            'user_activity' => $this->getUserActivity()
        ];
    }

    /**
     * Obtenir les packages les plus téléchargés
     */
    private function getTopPackages()
    {
        return PackageDownload::selectRaw('package_id, COUNT(*) as download_count')
            ->with('package')
            ->groupBy('package_id')
            ->orderBy('download_count', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * Obtenir la tendance des téléchargements
     */
    private function getDownloadsTrend()
    {
        $days = 30;
        $trend = [];

        for ($i = $days - 1; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $count = PackageDownload::whereDate('created_at', $date->toDateString())->count();
            
            $trend[] = [
                'date' => $date->format('Y-m-d'),
                'count' => $count
            ];
        }

        return $trend;
    }

    /**
     * Obtenir l'activité des utilisateurs
     */
    private function getUserActivity()
    {
        return PackageDownload::selectRaw('user_id, COUNT(*) as download_count')
            ->with('user')
            ->whereBetween('created_at', [Carbon::now()->subDays(7), Carbon::now()])
            ->groupBy('user_id')
            ->orderBy('download_count', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * Nettoyer les anciens téléchargements
     */
    public function cleanupOldDownloads($days = 90)
    {
        try {
            $cutoffDate = Carbon::now()->subDays($days);
            
            $deletedCount = PackageDownload::where('created_at', '<', $cutoffDate)
                ->where('status', 0) // Seulement les inactifs
                ->delete();

            return $deletedCount;
        } catch (\Exception $e) {
            \Log::error('Error cleaning up old downloads: ' . $e->getMessage());
            return 0;
        }
    }
} 
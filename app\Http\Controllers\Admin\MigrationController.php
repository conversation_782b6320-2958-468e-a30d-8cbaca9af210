<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class MigrationController extends Controller
{
    /**
     * Afficher la page de gestion des migrations
     */
    public function index()
    {
        $migrations = $this->getMigrationStatus();
        $pendingMigrations = $this->getPendingMigrations();
        
        return view('admin.migrations.index', compact('migrations', 'pendingMigrations'));
    }
    
    /**
     * Exécuter les migrations en attente
     */
    public function runMigrations(Request $request)
    {
        try {
            // Vérifier les permissions
            if (!auth()->user()->hasRole('admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Accès non autorisé'
                ], 403);
            }
            
            // Exécuter les migrations
            $output = [];
            $exitCode = 0;
            
            // Capturer la sortie de la commande
            ob_start();
            Artisan::call('migrate', ['--force' => true]);
            $output[] = ob_get_clean();
            
            // Vérifier s'il y a des erreurs
            if (Artisan::output()) {
                $output[] = Artisan::output();
            }
            
            // Vérifier si les nouvelles tables existent
            $tablesCreated = [];
            $requiredTables = ['api_keys', 'api_key_logs'];
            
            foreach ($requiredTables as $table) {
                if (Schema::hasTable($table)) {
                    $tablesCreated[] = $table;
                }
            }
            
            return response()->json([
                'success' => true,
                'message' => 'Migrations exécutées avec succès',
                'output' => $output,
                'tables_created' => $tablesCreated,
                'pending_migrations' => $this->getPendingMigrations()
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'exécution des migrations',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Vérifier le statut des migrations
     */
    public function checkStatus()
    {
        $migrations = $this->getMigrationStatus();
        $pendingMigrations = $this->getPendingMigrations();
        
        return response()->json([
            'success' => true,
            'migrations' => $migrations,
            'pending_count' => count($pendingMigrations),
            'pending_migrations' => $pendingMigrations
        ]);
    }
    
    /**
     * Obtenir le statut des migrations
     */
    private function getMigrationStatus()
    {
        try {
            $migrations = [];
            
            // Vérifier si la table migrations existe
            if (Schema::hasTable('migrations')) {
                $ranMigrations = DB::table('migrations')->pluck('migration')->toArray();
                
                // Obtenir toutes les migrations disponibles
                $files = glob(database_path('migrations/*.php'));
                
                foreach ($files as $file) {
                    $filename = basename($file, '.php');
                    $migrations[] = [
                        'migration' => $filename,
                        'ran' => in_array($filename, $ranMigrations),
                        'file_exists' => true
                    ];
                }
            }
            
            return $migrations;
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * Obtenir les migrations en attente
     */
    private function getPendingMigrations()
    {
        try {
            $pending = [];
            
            // Vérifier les migrations spécifiques à l'API
            $apiMigrations = [
                '2024_01_15_130000_create_api_keys_table',
                '2024_01_15_140000_create_api_key_logs_table'
            ];
            
            foreach ($apiMigrations as $migration) {
                if (!$this->isMigrationRan($migration)) {
                    $pending[] = $migration;
                }
            }
            
            return $pending;
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * Vérifier si une migration a été exécutée
     */
    private function isMigrationRan($migration)
    {
        try {
            return DB::table('migrations')->where('migration', $migration)->exists();
        } catch (\Exception $e) {
            return false;
        }
    }
} 
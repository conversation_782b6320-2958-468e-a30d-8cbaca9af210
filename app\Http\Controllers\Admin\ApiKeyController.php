<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ApiKey;
use App\Models\ApiKeyLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ApiKeysExport;

class ApiKeyController extends Controller
{
    /**
     * Afficher la liste des clés API
     */
    public function index(Request $request)
    {
        $query = ApiKey::with(['user'])
            ->withCount(['logs as request_count' => function($query) {
                $query->select(\DB::raw('count(*)'));
            }]);

        // Filtres
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->filled('permission')) {
            $query->whereJsonContains('permissions', $request->permission);
        }

        $apiKeys = $query->orderBy('created_at', 'desc')->paginate(20);
        
        // Statistiques globales
        $totalKeys = ApiKey::count();
        $activeUsers = User::whereHas('apiKeys')->count();
        $totalRequests = ApiKeyLog::count();
        $todayRequests = ApiKeyLog::whereDate('created_at', Carbon::today())->count();
        
        // Liste des utilisateurs pour le filtre
        $users = User::whereHas('apiKeys')->orderBy('name')->get();

        return view('admin.api-keys.index', compact(
            'apiKeys',
            'totalKeys',
            'activeUsers',
            'totalRequests',
            'todayRequests',
            'users'
        ));
    }

    /**
     * Créer une nouvelle clé API
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'name' => 'required|string|max:255',
            'permissions' => 'required|array|min:1',
            'permissions.*' => 'in:read,write,admin'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
        $apiKey = ApiKey::create([
            'user_id' => $request->user_id,
            'name' => $request->name,
                'key' => 'pk_' . Str::random(64),
                'permissions' => $request->permissions,
                'is_active' => true,
                'last_used_at' => null
            ]);

            Log::info('Admin created API key', [
                'admin_id' => auth()->id(),
                'user_id' => $request->user_id,
                'api_key_id' => $apiKey->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'API key created successfully',
                'data' => [
                    'id' => $apiKey->id,
                    'name' => $apiKey->name,
                    'key' => $apiKey->key
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('Admin API key creation failed', [
                'admin_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create API key'
            ], 500);
        }
    }

    /**
     * Obtenir les détails d'une clé API
     */
    public function show(ApiKey $apiKey): JsonResponse
    {
        $apiKey->load('user');

        // Statistiques d'utilisation
        $stats = [
            'total_requests' => ApiKeyLog::where('api_key_id', $apiKey->id)->count(),
            'success_rate' => $this->calculateSuccessRate($apiKey->id),
            'this_month' => ApiKeyLog::where('api_key_id', $apiKey->id)
                ->where('created_at', '>=', Carbon::now()->startOfMonth())
                ->count()
        ];

        // Activité récente
        $recentActivity = ApiKeyLog::where('api_key_id', $apiKey->id)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get(['endpoint', 'status', 'created_at']);

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $apiKey->id,
                'name' => $apiKey->name,
                'permissions' => $apiKey->permissions,
                'is_active' => $apiKey->is_active,
                'created_at' => $apiKey->created_at->format('M d, Y H:i'),
                'last_used_at' => $apiKey->last_used_at ? $apiKey->last_used_at->format('M d, Y H:i') : null,
                'user' => [
                    'id' => $apiKey->user->id,
                    'name' => $apiKey->user->name,
                    'email' => $apiKey->user->email
                ],
                'stats' => $stats,
                'recent_activity' => $recentActivity->map(function($log) {
                    return [
                        'endpoint' => $log->endpoint,
                        'status' => $log->status,
                        'created_at' => $log->created_at->format('M d, Y H:i')
                    ];
                })
            ]
        ]);
    }

    /**
     * Basculer le statut d'une clé API
     */
    public function toggleStatus(ApiKey $apiKey): JsonResponse
    {
        try {
            $apiKey->update([
                'is_active' => !$apiKey->is_active
            ]);

            Log::info('Admin toggled API key status', [
                'admin_id' => auth()->id(),
                'api_key_id' => $apiKey->id,
                'new_status' => $apiKey->is_active
            ]);

            return response()->json([
                'success' => true,
                'message' => 'API key status updated successfully',
                'data' => [
                    'id' => $apiKey->id,
                    'is_active' => $apiKey->is_active
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Admin API key status toggle failed', [
                'admin_id' => auth()->id(),
                'api_key_id' => $apiKey->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update API key status'
            ], 500);
        }
    }

    /**
     * Supprimer une clé API
     */
    public function destroy(ApiKey $apiKey): JsonResponse
    {
        try {
            // Supprimer les logs associés
            ApiKeyLog::where('api_key_id', $apiKey->id)->delete();
            
            // Supprimer la clé
            $apiKey->delete();

            Log::info('Admin deleted API key', [
                'admin_id' => auth()->id(),
                'api_key_id' => $apiKey->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'API key deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Admin API key deletion failed', [
                'admin_id' => auth()->id(),
                'api_key_id' => $apiKey->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete API key'
            ], 500);
        }
    }

    /**
     * Actions en lot
     */
    public function bulkToggleStatus(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'keys' => 'required|array|min:1',
            'keys.*' => 'exists:api_keys,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $apiKeys = ApiKey::whereIn('id', $request->keys)->get();
            
            foreach ($apiKeys as $apiKey) {
                $apiKey->update(['is_active' => !$apiKey->is_active]);
            }

            Log::info('Admin bulk toggled API keys status', [
                'admin_id' => auth()->id(),
                'keys_count' => count($request->keys)
            ]);

            return response()->json([
                'success' => true,
                'message' => count($request->keys) . ' API keys status updated successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Admin bulk API key status toggle failed', [
                'admin_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update API keys status'
            ], 500);
        }
    }

    /**
     * Suppression en lot
     */
    public function bulkDelete(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'keys' => 'required|array|min:1',
            'keys.*' => 'exists:api_keys,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Supprimer les logs associés
            ApiKeyLog::whereIn('api_key_id', $request->keys)->delete();
            
            // Supprimer les clés
            ApiKey::whereIn('id', $request->keys)->delete();

            Log::info('Admin bulk deleted API keys', [
                'admin_id' => auth()->id(),
                'keys_count' => count($request->keys)
            ]);

            return response()->json([
                'success' => true,
                'message' => count($request->keys) . ' API keys deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Admin bulk API key deletion failed', [
                'admin_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete API keys'
            ], 500);
        }
    }

    /**
     * Exporter les clés API
     */
    public function export(Request $request)
    {
        $query = ApiKey::with(['user']);

        // Appliquer les mêmes filtres que dans index()
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->filled('permission')) {
            $query->whereJsonContains('permissions', $request->permission);
        }

        $apiKeys = $query->get();

        $filename = 'api-keys-' . Carbon::now()->format('Y-m-d-H-i-s') . '.xlsx';

        return Excel::download(new ApiKeysExport($apiKeys), $filename);
    }

    /**
     * Statistiques globales
     */
    public function stats(): JsonResponse
    {
        $stats = [
            'total_keys' => ApiKey::count(),
            'active_keys' => ApiKey::where('is_active', true)->count(),
            'total_users' => User::whereHas('apiKeys')->count(),
            'total_requests' => ApiKeyLog::count(),
            'today_requests' => ApiKeyLog::whereDate('created_at', Carbon::today())->count(),
            'this_month_requests' => ApiKeyLog::where('created_at', '>=', Carbon::now()->startOfMonth())->count(),
            'popular_endpoints' => ApiKeyLog::selectRaw('endpoint, COUNT(*) as count')
                ->groupBy('endpoint')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get(),
            'requests_by_gateway' => ApiKeyLog::selectRaw('gateway, COUNT(*) as count')
                ->whereNotNull('gateway')
                ->groupBy('gateway')
                ->orderBy('count', 'desc')
                ->get(),
            'daily_requests' => ApiKeyLog::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->where('created_at', '>=', Carbon::now()->subDays(30))
                ->groupBy('date')
                ->orderBy('date')
                ->get()
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Calculer le taux de succès
     */
    private function calculateSuccessRate(int $apiKeyId): string
    {
        $total = ApiKeyLog::where('api_key_id', $apiKeyId)->count();
        
        if ($total === 0) {
            return '0%';
        }
        
        $successful = ApiKeyLog::where('api_key_id', $apiKeyId)
            ->where('status', 'success')
            ->count();
            
        return round(($successful / $total) * 100, 1) . '%';
    }
} 
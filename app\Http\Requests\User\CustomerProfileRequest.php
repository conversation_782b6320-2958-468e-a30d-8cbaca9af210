<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;

class CustomerProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            "name" => ['required', 'string', 'max:255'],
            "mobile" => 'bail|required|min:6|unique:users,mobile,' . $this->id,
            'email' => 'bail|required|email|unique:users,email,' . $this->id,
            "company_name" =>  'bail|required|max:195',
            "billing_country" =>  'bail|required|max:195',
            "billing_city" =>  'bail|required|max:195',
            "billing_zip_code" =>  'bail|required|max:195',
            "billing_address" =>  'bail|required|max:195',
            "billing_state" =>  'bail|required|max:195',
        ];
        return $rules;
    }
}

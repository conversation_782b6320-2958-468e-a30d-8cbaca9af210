<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Subscription;
use App\Models\Customer;
use App\Models\Order;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SubscriptionController extends Controller
{
    protected $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Créer un abonnement
     * POST /api/v1/subscriptions
     */
    public function createSubscription(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_id' => 'required|exists:products,id',
                'customer_email' => 'required|email',
                'customer_name' => 'required|string|max:255',
                'plan_id' => 'required|exists:plans,id',
                'gateway' => 'required|string|in:stripe,paypal,razorpay,mollie',
                'return_url' => 'required|url',
                'cancel_url' => 'required|url',
                'webhook_url' => 'nullable|url',
                'trial_days' => 'nullable|integer|min:0|max:30',
                'metadata' => 'nullable|array',
                'coupon_code' => 'nullable|string|exists:coupons,code'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $product = Product::findOrFail($request->product_id);
            $plan = Plan::findOrFail($request->plan_id);

            // Vérifier que le plan appartient au produit
            if ($plan->product_id !== $product->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Plan does not belong to the specified product'
                ], 400);
            }

            // Créer ou récupérer le client
            $customer = Customer::firstOrCreate(
                ['email' => $request->customer_email],
                [
                    'name' => $request->customer_name,
                    'user_id' => $product->user_id,
                    'status' => true
                ]
            );

            // Vérifier si le client a déjà un abonnement actif
            $existingSubscription = Subscription::where('customer_id', $customer->id)
                ->where('product_id', $product->id)
                ->whereIn('status', ['active', 'trialing'])
                ->first();

            if ($existingSubscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer already has an active subscription',
                    'subscription_id' => $existingSubscription->id
                ], 409);
            }

            // Créer l'abonnement
            $subscriptionData = [
                'user_id' => $product->user_id,
                'customer_id' => $customer->id,
                'product_id' => $product->id,
                'plan_id' => $plan->id,
                'gateway' => $request->gateway,
                'status' => 'pending',
                'trial_days' => $request->trial_days ?? 0,
                'return_url' => $request->return_url,
                'cancel_url' => $request->cancel_url,
                'webhook_url' => $request->webhook_url,
                'metadata' => $request->metadata ?? [],
                'coupon_code' => $request->coupon_code
            ];

            $subscriptionResult = $this->subscriptionService->createSubscription($subscriptionData);

            if (!$subscriptionResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $subscriptionResult['message']
                ], 400);
            }

            Log::info('API Subscription created', [
                'subscription_id' => $subscriptionResult['subscription_id'],
                'product_id' => $product->id,
                'customer_id' => $customer->id,
                'plan_id' => $plan->id,
                'gateway' => $request->gateway
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Subscription created successfully',
                'data' => [
                    'subscription_id' => $subscriptionResult['subscription_id'],
                    'checkout_url' => $subscriptionResult['checkout_url'],
                    'trial_end' => $subscriptionResult['trial_end'] ?? null,
                    'next_billing_date' => $subscriptionResult['next_billing_date'] ?? null
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('API Subscription creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'SUBSCRIPTION_CREATION_FAILED'
            ], 500);
        }
    }

    /**
     * Obtenir le statut d'un abonnement
     * GET /api/v1/subscriptions/{subscription_id}
     */
    public function getSubscription(string $subscriptionId): JsonResponse
    {
        try {
            $subscription = Subscription::with(['customer', 'product', 'plan'])
                ->where('id', $subscriptionId)
                ->orWhere('external_subscription_id', $subscriptionId)
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription not found'
                ], 404);
            }

            $subscriptionData = [
                'id' => $subscription->id,
                'external_subscription_id' => $subscription->external_subscription_id,
                'status' => $subscription->status,
                'gateway' => $subscription->gateway,
                'trial_end' => $subscription->trial_end,
                'current_period_start' => $subscription->current_period_start,
                'current_period_end' => $subscription->current_period_end,
                'canceled_at' => $subscription->canceled_at,
                'ended_at' => $subscription->ended_at,
                'created_at' => $subscription->created_at,
                'updated_at' => $subscription->updated_at,
                'customer' => [
                    'id' => $subscription->customer->id,
                    'name' => $subscription->customer->name,
                    'email' => $subscription->customer->email
                ],
                'product' => [
                    'id' => $subscription->product->id,
                    'name' => $subscription->product->name
                ],
                'plan' => [
                    'id' => $subscription->plan->id,
                    'name' => $subscription->plan->name,
                    'price' => $subscription->plan->price,
                    'interval' => $subscription->plan->interval
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $subscriptionData
            ]);

        } catch (\Exception $e) {
            Log::error('API Subscription status check failed', [
                'subscription_id' => $subscriptionId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'SUBSCRIPTION_STATUS_CHECK_FAILED'
            ], 500);
        }
    }

    /**
     * Annuler un abonnement
     * POST /api/v1/subscriptions/{subscription_id}/cancel
     */
    public function cancelSubscription(Request $request, string $subscriptionId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'cancel_at_period_end' => 'nullable|boolean',
                'reason' => 'nullable|string|max:255'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $subscription = Subscription::where('id', $subscriptionId)
                ->orWhere('external_subscription_id', $subscriptionId)
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription not found'
                ], 404);
            }

            if (!in_array($subscription->status, ['active', 'trialing'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription cannot be cancelled',
                    'current_status' => $subscription->status
                ], 400);
            }

            $cancelResult = $this->subscriptionService->cancelSubscription(
                $subscription,
                $request->cancel_at_period_end ?? true,
                $request->reason
            );

            if (!$cancelResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $cancelResult['message']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Subscription cancelled successfully',
                'data' => [
                    'subscription_id' => $subscription->id,
                    'status' => $subscription->status,
                    'canceled_at' => $subscription->canceled_at,
                    'ends_at' => $subscription->ends_at
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('API Subscription cancellation failed', [
                'subscription_id' => $subscriptionId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'SUBSCRIPTION_CANCELLATION_FAILED'
            ], 500);
        }
    }

    /**
     * Réactiver un abonnement
     * POST /api/v1/subscriptions/{subscription_id}/reactivate
     */
    public function reactivateSubscription(string $subscriptionId): JsonResponse
    {
        try {
            $subscription = Subscription::where('id', $subscriptionId)
                ->orWhere('external_subscription_id', $subscriptionId)
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription not found'
                ], 404);
            }

            if ($subscription->status !== 'canceled') {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription cannot be reactivated',
                    'current_status' => $subscription->status
                ], 400);
            }

            $reactivateResult = $this->subscriptionService->reactivateSubscription($subscription);

            if (!$reactivateResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $reactivateResult['message']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Subscription reactivated successfully',
                'data' => [
                    'subscription_id' => $subscription->id,
                    'status' => $subscription->status,
                    'current_period_end' => $subscription->current_period_end
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('API Subscription reactivation failed', [
                'subscription_id' => $subscriptionId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'SUBSCRIPTION_REACTIVATION_FAILED'
            ], 500);
        }
    }

    /**
     * Changer le plan d'un abonnement
     * POST /api/v1/subscriptions/{subscription_id}/change-plan
     */
    public function changePlan(Request $request, string $subscriptionId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'plan_id' => 'required|exists:plans,id',
                'prorate' => 'nullable|boolean',
                'effective_date' => 'nullable|date|after:today'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $subscription = Subscription::with(['product', 'plan'])
                ->where('id', $subscriptionId)
                ->orWhere('external_subscription_id', $subscriptionId)
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription not found'
                ], 404);
            }

            $newPlan = Plan::findOrFail($request->plan_id);

            // Vérifier que le nouveau plan appartient au même produit
            if ($newPlan->product_id !== $subscription->product_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'New plan must belong to the same product'
                ], 400);
            }

            $changeResult = $this->subscriptionService->changePlan(
                $subscription,
                $newPlan,
                $request->prorate ?? true,
                $request->effective_date
            );

            if (!$changeResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $changeResult['message']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Plan changed successfully',
                'data' => [
                    'subscription_id' => $subscription->id,
                    'old_plan' => [
                        'id' => $subscription->plan->id,
                        'name' => $subscription->plan->name,
                        'price' => $subscription->plan->price
                    ],
                    'new_plan' => [
                        'id' => $newPlan->id,
                        'name' => $newPlan->name,
                        'price' => $newPlan->price
                    ],
                    'effective_date' => $changeResult['effective_date'] ?? null,
                    'proration_amount' => $changeResult['proration_amount'] ?? null
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('API Subscription plan change failed', [
                'subscription_id' => $subscriptionId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'SUBSCRIPTION_PLAN_CHANGE_FAILED'
            ], 500);
        }
    }

    /**
     * Obtenir l'historique des factures d'un abonnement
     * GET /api/v1/subscriptions/{subscription_id}/invoices
     */
    public function getSubscriptionInvoices(string $subscriptionId): JsonResponse
    {
        try {
            $subscription = Subscription::where('id', $subscriptionId)
                ->orWhere('external_subscription_id', $subscriptionId)
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription not found'
                ], 404);
            }

            $invoices = Order::where('subscription_id', $subscription->id)
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($invoice) {
                    return [
                        'id' => $invoice->id,
                        'external_order_id' => $invoice->external_order_id,
                        'amount' => $invoice->amount,
                        'currency' => $invoice->currency,
                        'status' => $invoice->status,
                        'gateway' => $invoice->gateway,
                        'created_at' => $invoice->created_at,
                        'paid_at' => $invoice->paid_at
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'subscription_id' => $subscription->id,
                    'total_invoices' => $invoices->count(),
                    'invoices' => $invoices
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('API Subscription invoices failed', [
                'subscription_id' => $subscriptionId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'SUBSCRIPTION_INVOICES_FAILED'
            ], 500);
        }
    }

    /**
     * Obtenir les statistiques des abonnements
     * GET /api/v1/subscriptions/stats
     */
    public function getSubscriptionStats(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_id' => 'required|exists:users,id',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after:start_date',
                'status' => 'nullable|string|in:active,trialing,canceled,expired'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $query = Subscription::where('user_id', $request->user_id);

            if ($request->start_date) {
                $query->where('created_at', '>=', $request->start_date);
            }

            if ($request->end_date) {
                $query->where('created_at', '<=', $request->end_date);
            }

            if ($request->status) {
                $query->where('status', $request->status);
            }

            $stats = [
                'total_subscriptions' => $query->count(),
                'active_subscriptions' => $query->where('status', 'active')->count(),
                'trialing_subscriptions' => $query->where('status', 'trialing')->count(),
                'canceled_subscriptions' => $query->where('status', 'canceled')->count(),
                'expired_subscriptions' => $query->where('status', 'expired')->count(),
                'monthly_recurring_revenue' => $query->where('status', 'active')
                    ->join('plans', 'subscriptions.plan_id', '=', 'plans.id')
                    ->sum('plans.price'),
                'trial_conversion_rate' => $this->calculateTrialConversionRate($request->user_id),
                'churn_rate' => $this->calculateChurnRate($request->user_id),
                'average_subscription_value' => $query->join('plans', 'subscriptions.plan_id', '=', 'plans.id')
                    ->avg('plans.price')
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error('API Subscription stats failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'SUBSCRIPTION_STATS_FAILED'
            ], 500);
        }
    }

    /**
     * Calculer le taux de conversion des essais
     */
    private function calculateTrialConversionRate(int $userId): float
    {
        $totalTrials = Subscription::where('user_id', $userId)
            ->where('trial_days', '>', 0)
            ->count();

        if ($totalTrials === 0) {
            return 0;
        }

        $convertedTrials = Subscription::where('user_id', $userId)
            ->where('trial_days', '>', 0)
            ->where('status', 'active')
            ->count();

        return round(($convertedTrials / $totalTrials) * 100, 2);
    }

    /**
     * Calculer le taux de désabonnement
     */
    private function calculateChurnRate(int $userId): float
    {
        $thirtyDaysAgo = Carbon::now()->subDays(30);
        
        $activeSubscriptions = Subscription::where('user_id', $userId)
            ->where('status', 'active')
            ->where('created_at', '<', $thirtyDaysAgo)
            ->count();

        if ($activeSubscriptions === 0) {
            return 0;
        }

        $canceledSubscriptions = Subscription::where('user_id', $userId)
            ->where('status', 'canceled')
            ->where('canceled_at', '>=', $thirtyDaysAgo)
            ->count();

        return round(($canceledSubscriptions / $activeSubscriptions) * 100, 2);
    }
} 
<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\DigitalDownload;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;

class DownloadController extends Controller
{
    /**
     * Télécharger un produit digital
     * GET /api/v1/downloads/{token}
     */
    public function download(string $token): JsonResponse|StreamedResponse
    {
        try {
            $download = DigitalDownload::where('download_token', $token)
                ->with(['product', 'customer'])
                ->first();

            if (!$download) {
                return response()->json([
                    'success' => false,
                    'message' => 'Download link not found or expired'
                ], 404);
            }

            // Vérifier si le téléchargement est actif
            if (!$download->isActive()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Download link has expired',
                    'expired_at' => $download->expires_at
                ], 410);
            }

            // Vérifier les limites de téléchargement
            if (!$download->checkDownloadLimits()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Download limit exceeded'
                ], 429);
            }

            // Vérifier si le fichier existe
            if (!$download->product->hasDigitalFile()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Digital file not found'
                ], 404);
            }

            $filePath = storage_path('app/' . $download->product->digital_file_path);
            
            if (!file_exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File not found on server'
                ], 404);
            }

            // Vérifier si le fichier est protégé par mot de passe
            if ($download->product->isPasswordProtected() && !$download->password_verified) {
                return response()->json([
                    'success' => false,
                    'message' => 'Password required for download',
                    'requires_password' => true
                ], 401);
            }

            // Incrémenter le compteur de téléchargements
            $download->incrementDownloadCount();

            // Log du téléchargement
            Log::info('Digital product downloaded via API', [
                'download_id' => $download->id,
                'product_id' => $download->product_id,
                'customer_id' => $download->customer_id,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent()
            ]);

            // Retourner le fichier pour téléchargement
            return Storage::disk('private')->download(
                $download->product->digital_file_path,
                $download->product->digital_file_name,
                [
                    'Content-Type' => 'application/octet-stream',
                    'Content-Disposition' => 'attachment; filename="' . $download->product->digital_file_name . '"'
                ]
            );

        } catch (\Exception $e) {
            Log::error('API Download failed', [
                'token' => $token,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'DOWNLOAD_FAILED'
            ], 500);
        }
    }

    /**
     * Vérifier le mot de passe pour un téléchargement protégé
     * POST /api/v1/downloads/{token}/verify-password
     */
    public function verifyPassword(Request $request, string $token): JsonResponse
    {
        try {
            $validator = \Validator::make($request->all(), [
                'password' => 'required|string|max:255'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $download = DigitalDownload::where('download_token', $token)
                ->with(['product'])
                ->first();

            if (!$download) {
                return response()->json([
                    'success' => false,
                    'message' => 'Download link not found or expired'
                ], 404);
            }

            // Vérifier si le téléchargement est actif
            if (!$download->isActive()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Download link has expired'
                ], 410);
            }

            // Vérifier si le fichier est protégé par mot de passe
            if (!$download->product->isPasswordProtected()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This download does not require a password'
                ], 400);
            }

            // Vérifier le mot de passe
            if ($download->product->file_password !== $request->password) {
                Log::warning('Failed password verification for download', [
                    'download_id' => $download->id,
                    'ip_address' => request()->ip()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Incorrect password'
                ], 401);
            }

            // Marquer le mot de passe comme vérifié
            $download->update(['password_verified' => true]);

            Log::info('Password verified for download', [
                'download_id' => $download->id,
                'ip_address' => request()->ip()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Password verified successfully',
                'data' => [
                    'download_url' => route('api.v1.downloads.download', $token),
                    'expires_at' => $download->expires_at
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('API Password verification failed', [
                'token' => $token,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'PASSWORD_VERIFICATION_FAILED'
            ], 500);
        }
    }

    /**
     * Obtenir les informations d'un téléchargement
     * GET /api/v1/downloads/{token}/info
     */
    public function getDownloadInfo(string $token): JsonResponse
    {
        try {
            $download = DigitalDownload::where('download_token', $token)
                ->with(['product', 'customer'])
                ->first();

            if (!$download) {
                return response()->json([
                    'success' => false,
                    'message' => 'Download link not found or expired'
                ], 404);
            }

            $info = [
                'download_id' => $download->id,
                'product' => [
                    'id' => $download->product->id,
                    'name' => $download->product->name,
                    'file_name' => $download->product->digital_file_name,
                    'file_size' => $download->product->formatted_file_size,
                    'file_type' => $download->product->digital_file_type,
                    'password_protected' => $download->product->isPasswordProtected()
                ],
                'customer' => [
                    'id' => $download->customer->id,
                    'name' => $download->customer->name,
                    'email' => $download->customer->email
                ],
                'status' => $download->status,
                'is_active' => $download->isActive(),
                'is_expired' => $download->isExpired(),
                'download_count' => $download->download_count,
                'download_limit' => $download->product->download_limit,
                'download_limit_type' => $download->product->download_limit_type,
                'expires_at' => $download->expires_at,
                'created_at' => $download->created_at,
                'last_download_at' => $download->last_download_at
            ];

            return response()->json([
                'success' => true,
                'data' => $info
            ]);

        } catch (\Exception $e) {
            Log::error('API Download info failed', [
                'token' => $token,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'DOWNLOAD_INFO_FAILED'
            ], 500);
        }
    }

    /**
     * Régénérer un token de téléchargement
     * POST /api/v1/downloads/{token}/regenerate
     */
    public function regenerateToken(string $token): JsonResponse
    {
        try {
            $download = DigitalDownload::where('download_token', $token)
                ->with(['product'])
                ->first();

            if (!$download) {
                return response()->json([
                    'success' => false,
                    'message' => 'Download link not found or expired'
                ], 404);
            }

            // Vérifier si le téléchargement est actif
            if (!$download->isActive()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Download link has expired'
                ], 410);
            }

            // Générer un nouveau token
            $newToken = $download->generateDownloadToken();

            Log::info('Download token regenerated', [
                'download_id' => $download->id,
                'old_token' => $token,
                'new_token' => $newToken
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Token regenerated successfully',
                'data' => [
                    'new_token' => $newToken,
                    'download_url' => route('api.v1.downloads.download', $newToken),
                    'expires_at' => $download->expires_at
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('API Token regeneration failed', [
                'token' => $token,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'TOKEN_REGENERATION_FAILED'
            ], 500);
        }
    }
} 
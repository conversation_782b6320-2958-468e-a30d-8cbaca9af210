<?php


namespace App\Http\Services\Payment;

class Payment
{
    public $provider = null;

    public function __construct($method, $object = [])
    {
        $classPath = getPaymentServiceClass($method);
        $this->provider = new $classPath($method, $object);
    }

    public function makePayment($amount, $postData = null)
    {
        $res = $this->provider->makePayment($amount, $postData);
        return $res;
    }

    public function paymentConfirmation($payment_id, $payer_id = null, $token = null)
    {
        if (is_null($payer_id)) {
            return $this->provider->paymentConfirmation($payment_id, $token);
        }
        return $this->provider->paymentConfirmation($payment_id, $payer_id);
    }
    public function subscribe($productId, $data=NULL)
    {
        $res = $this->provider->subscribe($productId, $data);
        return $res;
    }

    public function subscribeSaas($productId, $data=NULL)
    {
        $res = $this->provider->subscribeSaas($productId, $data);
        return $res;
    }

    public function saveProduct($data)
    {
        return $this->provider->saveProduct($data);
    }

    public function saveProductSaas($data)
    {
        return $this->provider->saveProductSaas($data);
    }

    public function handleWebhook($request)
    {
        return $this->provider->handleWebhook($request);
    }

}

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Product;
use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;

class OrderController extends Controller
{
    /**
     * Lister toutes les commandes de l'utilisateur
     */
    public function index(Request $request)
    {
        $orders = Order::where('user_id', $request->api_user->id)
            ->with(['product', 'plan'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json([
            'data' => $orders->items(),
            'pagination' => [
                'current_page' => $orders->currentPage(),
                'last_page' => $orders->lastPage(),
                'per_page' => $orders->perPage(),
                'total' => $orders->total()
            ]
        ]);
    }

    /**
     * Afficher une commande spécifique
     */
    public function show(Request $request, $id)
    {
        $order = Order::where('user_id', $request->api_user->id)
            ->with(['product', 'plan', 'user'])
            ->find($id);

        if (!$order) {
            return response()->json([
                'error' => 'Commande non trouvée'
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'data' => $order
        ]);
    }

    /**
     * Créer une nouvelle commande
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required_without:plan_id|exists:products,id',
            'plan_id' => 'required_without:product_id|exists:plans,id',
            'quantity' => 'required|integer|min:1',
            'gateway_id' => 'required|exists:gateways,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Données invalides',
                'errors' => $validator->errors()
            ], Response::HTTP_BAD_REQUEST);
        }

        // Logique de création de commande
        $orderData = [
            'user_id' => $request->api_user->id,
            'quantity' => $request->quantity,
            'gateway_id' => $request->gateway_id,
            'status' => 'pending'
        ];

        if ($request->product_id) {
            $product = Product::find($request->product_id);
            $orderData['product_id'] = $request->product_id;
            $orderData['amount'] = $product->price * $request->quantity;
        } else {
            $plan = Plan::find($request->plan_id);
            $orderData['plan_id'] = $request->plan_id;
            $orderData['amount'] = $plan->price * $request->quantity;
        }

        $order = Order::create($orderData);

        return response()->json([
            'message' => 'Commande créée avec succès',
            'data' => $order->load(['product', 'plan'])
        ], Response::HTTP_CREATED);
    }

    /**
     * Annuler une commande
     */
    public function cancel(Request $request, $id)
    {
        $order = Order::where('user_id', $request->api_user->id)
            ->where('id', $id)
            ->first();

        if (!$order) {
            return response()->json([
                'error' => 'Commande non trouvée'
            ], Response::HTTP_NOT_FOUND);
        }

        if ($order->status !== 'pending') {
            return response()->json([
                'error' => 'Impossible d\'annuler cette commande'
            ], Response::HTTP_BAD_REQUEST);
        }

        $order->update(['status' => 'cancelled']);

        return response()->json([
            'message' => 'Commande annulée avec succès',
            'data' => $order
        ]);
    }

    /**
     * Obtenir les statistiques des commandes
     */
    public function stats(Request $request)
    {
        $userId = $request->api_user->id;

        $stats = [
            'total_orders' => Order::where('user_id', $userId)->count(),
            'pending_orders' => Order::where('user_id', $userId)->where('status', 'pending')->count(),
            'completed_orders' => Order::where('user_id', $userId)->where('status', 'completed')->count(),
            'cancelled_orders' => Order::where('user_id', $userId)->where('status', 'cancelled')->count(),
            'total_amount' => Order::where('user_id', $userId)->where('status', 'completed')->sum('amount')
        ];

        return response()->json([
            'data' => $stats
        ]);
    }
} 
<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Plan;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ProductController extends Controller
{
    /**
     * Lister les produits
     * GET /api/v1/products
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_id' => 'required|exists:users,id',
                'category_id' => 'nullable|exists:categories,id',
                'is_digital' => 'nullable|boolean',
                'status' => 'nullable|boolean',
                'search' => 'nullable|string|max:255',
                'per_page' => 'nullable|integer|min:1|max:100',
                'sort_by' => 'nullable|string|in:name,price,created_at,updated_at',
                'sort_order' => 'nullable|string|in:asc,desc'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $query = Product::where('user_id', $request->user_id)
                ->with(['category', 'plans']);

            // Filtres
            if ($request->category_id) {
                $query->where('category_id', $request->category_id);
            }

            if ($request->has('is_digital')) {
                $query->where('is_digital', $request->is_digital);
            }

            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            if ($request->search) {
                $query->where(function ($q) use ($request) {
                    $q->where('name', 'like', '%' . $request->search . '%')
                      ->orWhere('description', 'like', '%' . $request->search . '%');
                });
            }

            // Tri
            $sortBy = $request->sort_by ?? 'created_at';
            $sortOrder = $request->sort_order ?? 'desc';
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->per_page ?? 15;
            $products = $query->paginate($perPage);

            // Formater les données
            $formattedProducts = $products->getCollection()->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'description' => $product->description,
                    'price' => $product->price,
                    'currency' => $product->currency ?? 'USD',
                    'is_digital' => $product->is_digital,
                    'status' => $product->status,
                    'is_featured' => $product->is_featured,
                    'stock' => $product->stock,
                    'category' => $product->category ? [
                        'id' => $product->category->id,
                        'name' => $product->category->name
                    ] : null,
                    'plans' => $product->plans->map(function ($plan) {
                        return [
                            'id' => $plan->id,
                            'name' => $plan->name,
                            'price' => $plan->price,
                            'interval' => $plan->interval
                        ];
                    }),
                    'digital_info' => $product->is_digital ? [
                        'file_name' => $product->digital_file_name,
                        'file_size' => $product->formatted_file_size,
                        'file_type' => $product->digital_file_type,
                        'password_protected' => $product->isPasswordProtected(),
                        'download_limit' => $product->download_limit_text,
                        'expiry_days' => $product->download_expiry_days
                    ] : null,
                    'stats' => $product->getDownloadStats(),
                    'created_at' => $product->created_at,
                    'updated_at' => $product->updated_at
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'products' => $formattedProducts,
                    'pagination' => [
                        'current_page' => $products->currentPage(),
                        'last_page' => $products->lastPage(),
                        'per_page' => $products->perPage(),
                        'total' => $products->total(),
                        'from' => $products->firstItem(),
                        'to' => $products->lastItem()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('API Products listing failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'PRODUCTS_LISTING_FAILED'
            ], 500);
        }
    }

    /**
     * Obtenir un produit spécifique
     * GET /api/v1/products/{product_id}
     */
    public function show(string $productId): JsonResponse
    {
        try {
            $product = Product::with(['category', 'plans', 'digitalDownloads'])
                ->findOrFail($productId);

            $productData = [
                'id' => $product->id,
                'name' => $product->name,
                'description' => $product->description,
                'price' => $product->price,
                'currency' => $product->currency ?? 'USD',
                'is_digital' => $product->is_digital,
                'status' => $product->status,
                'is_featured' => $product->is_featured,
                'stock' => $product->stock,
                'sku' => $product->sku,
                'slug' => $product->slug,
                'category' => $product->category ? [
                    'id' => $product->category->id,
                    'name' => $product->category->name
                ] : null,
                'plans' => $product->plans->map(function ($plan) {
                    return [
                        'id' => $plan->id,
                        'name' => $plan->name,
                        'description' => $plan->description,
                        'price' => $plan->price,
                        'interval' => $plan->interval,
                        'trial_days' => $plan->trial_days,
                        'features' => $plan->features ?? []
                    ];
                }),
                'digital_info' => $product->is_digital ? [
                    'file_name' => $product->digital_file_name,
                    'file_size' => $product->formatted_file_size,
                    'file_type' => $product->digital_file_type,
                    'password_protected' => $product->isPasswordProtected(),
                    'share_method' => $product->share_method_text,
                    'download_limit' => $product->download_limit_text,
                    'expiry_days' => $product->download_expiry_days,
                    'auto_delivery' => $product->hasAutoDelivery(),
                    'delivery_status' => $product->delivery_status_text
                ] : null,
                'stats' => $product->getDownloadStats(),
                'created_at' => $product->created_at,
                'updated_at' => $product->updated_at
            ];

            return response()->json([
                'success' => true,
                'data' => $productData
            ]);

        } catch (\Exception $e) {
            Log::error('API Product show failed', [
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Product not found',
                'error_code' => 'PRODUCT_NOT_FOUND'
            ], 404);
        }
    }

    /**
     * Créer un produit
     * POST /api/v1/products
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'price' => 'required|numeric|min:0',
                'currency' => 'nullable|string|size:3',
                'category_id' => 'nullable|exists:categories,id',
                'is_digital' => 'nullable|boolean',
                'status' => 'nullable|boolean',
                'is_featured' => 'nullable|boolean',
                'stock' => 'nullable|integer|min:0',
                'sku' => 'nullable|string|max:255|unique:products,sku',
                'slug' => 'nullable|string|max:255|unique:products,slug',
                // Champs pour produits digitaux
                'digital_file' => 'nullable|file|max:102400', // 100MB max
                'share_method' => 'nullable|string|in:email,link,both',
                'password_protected' => 'nullable|boolean',
                'file_password' => 'nullable|string|max:255',
                'download_limit' => 'nullable|integer|min:1',
                'download_limit_type' => 'nullable|integer|in:1,2',
                'download_expiry_days' => 'nullable|integer|min:1|max:365',
                'auto_delivery' => 'nullable|boolean',
                'delivery_after_payment' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $productData = [
                'user_id' => $request->user_id,
                'name' => $request->name,
                'description' => $request->description,
                'price' => $request->price,
                'currency' => $request->currency ?? 'USD',
                'category_id' => $request->category_id,
                'is_digital' => $request->is_digital ?? false,
                'status' => $request->status ?? true,
                'is_featured' => $request->is_featured ?? false,
                'stock' => $request->stock ?? 0,
                'sku' => $request->sku,
                'slug' => $request->slug ?? \Str::slug($request->name)
            ];

            // Traitement du fichier digital si fourni
            if ($request->hasFile('digital_file') && $request->is_digital) {
                $file = $request->file('digital_file');
                $fileName = time() . '_' . $file->getClientOriginalName();
                $filePath = $file->storeAs('digital_products', $fileName, 'private');
                
                $productData['digital_file_path'] = $filePath;
                $productData['digital_file_name'] = $file->getClientOriginalName();
                $productData['digital_file_size'] = $file->getSize();
                $productData['digital_file_type'] = $file->getClientOriginalExtension();
            }

            // Ajouter les champs digitaux si applicable
            if ($request->is_digital) {
                $productData['share_method'] = $request->share_method ?? 'email';
                $productData['password_protected'] = $request->password_protected ?? false;
                $productData['file_password'] = $request->file_password;
                $productData['download_limit'] = $request->download_limit ?? 10;
                $productData['download_limit_type'] = $request->download_limit_type ?? 1;
                $productData['download_expiry_days'] = $request->download_expiry_days ?? 7;
                $productData['auto_delivery'] = $request->auto_delivery ?? true;
                $productData['delivery_after_payment'] = $request->delivery_after_payment ?? true;
            }

            $product = Product::create($productData);

            Log::info('API Product created', [
                'product_id' => $product->id,
                'user_id' => $request->user_id,
                'is_digital' => $product->is_digital
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Product created successfully',
                'data' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'is_digital' => $product->is_digital,
                    'status' => $product->status
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('API Product creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'PRODUCT_CREATION_FAILED'
            ], 500);
        }
    }

    /**
     * Mettre à jour un produit
     * PUT /api/v1/products/{product_id}
     */
    public function update(Request $request, string $productId): JsonResponse
    {
        try {
            $product = Product::findOrFail($productId);

            // Vérifier que l'utilisateur peut modifier ce produit
            if ($product->user_id != $request->user_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized to modify this product'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'price' => 'nullable|numeric|min:0',
                'currency' => 'nullable|string|size:3',
                'category_id' => 'nullable|exists:categories,id',
                'status' => 'nullable|boolean',
                'is_featured' => 'nullable|boolean',
                'stock' => 'nullable|integer|min:0',
                'sku' => 'nullable|string|max:255|unique:products,sku,' . $productId,
                'slug' => 'nullable|string|max:255|unique:products,slug,' . $productId,
                // Champs pour produits digitaux
                'digital_file' => 'nullable|file|max:102400',
                'share_method' => 'nullable|string|in:email,link,both',
                'password_protected' => 'nullable|boolean',
                'file_password' => 'nullable|string|max:255',
                'download_limit' => 'nullable|integer|min:1',
                'download_limit_type' => 'nullable|integer|in:1,2',
                'download_expiry_days' => 'nullable|integer|min:1|max:365',
                'auto_delivery' => 'nullable|boolean',
                'delivery_after_payment' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $updateData = $request->only([
                'name', 'description', 'price', 'currency', 'category_id',
                'status', 'is_featured', 'stock', 'sku', 'slug'
            ]);

            // Traitement du nouveau fichier digital si fourni
            if ($request->hasFile('digital_file') && $product->is_digital) {
                // Supprimer l'ancien fichier
                if ($product->digital_file_path) {
                    Storage::disk('private')->delete($product->digital_file_path);
                }

                $file = $request->file('digital_file');
                $fileName = time() . '_' . $file->getClientOriginalName();
                $filePath = $file->storeAs('digital_products', $fileName, 'private');
                
                $updateData['digital_file_path'] = $filePath;
                $updateData['digital_file_name'] = $file->getClientOriginalName();
                $updateData['digital_file_size'] = $file->getSize();
                $updateData['digital_file_type'] = $file->getClientOriginalExtension();
            }

            // Mettre à jour les champs digitaux si applicable
            if ($product->is_digital) {
                $digitalFields = [
                    'share_method', 'password_protected', 'file_password',
                    'download_limit', 'download_limit_type', 'download_expiry_days',
                    'auto_delivery', 'delivery_after_payment'
                ];

                foreach ($digitalFields as $field) {
                    if ($request->has($field)) {
                        $updateData[$field] = $request->$field;
                    }
                }
            }

            $product->update($updateData);

            Log::info('API Product updated', [
                'product_id' => $product->id,
                'user_id' => $request->user_id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Product updated successfully',
                'data' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'status' => $product->status
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('API Product update failed', [
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'PRODUCT_UPDATE_FAILED'
            ], 500);
        }
    }

    /**
     * Supprimer un produit
     * DELETE /api/v1/products/{product_id}
     */
    public function destroy(Request $request, string $productId): JsonResponse
    {
        try {
            $product = Product::findOrFail($productId);

            // Vérifier que l'utilisateur peut supprimer ce produit
            if ($product->user_id != $request->user_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized to delete this product'
                ], 403);
            }

            // Supprimer le fichier digital si présent
            if ($product->digital_file_path) {
                Storage::disk('private')->delete($product->digital_file_path);
            }

            $product->delete();

            Log::info('API Product deleted', [
                'product_id' => $productId,
                'user_id' => $request->user_id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Product deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('API Product deletion failed', [
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'PRODUCT_DELETION_FAILED'
            ], 500);
        }
    }

    /**
     * Obtenir les catégories
     * GET /api/v1/products/categories
     */
    public function getCategories(Request $request): JsonResponse
    {
        try {
            $categories = Category::where('user_id', $request->user_id)
                ->where('status', true)
                ->orderBy('name')
                ->get(['id', 'name', 'description']);

            return response()->json([
                'success' => true,
                'data' => $categories
            ]);

        } catch (\Exception $e) {
            Log::error('API Categories listing failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error_code' => 'CATEGORIES_LISTING_FAILED'
            ], 500);
        }
    }
} 
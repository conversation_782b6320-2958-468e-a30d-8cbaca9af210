<?php

namespace App\Http\Controllers\Saas\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\Gateway;
use App\Models\Product;

class GatewayController extends Controller
{
    /**
     * Afficher la page de gestion des passerelles
     */
    public function index()
    {
        $user = auth()->user();
        $gateways = Gateway::where('user_id', $user->id)->get();
        
        return view('saas.user.gateways.index', compact('gateways'));
    }

    /**
     * Stocker une nouvelle passerelle
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:stripe,paypal,razorpay,mollie',
            'is_active' => 'boolean',
            'credentials' => 'required|array',
            'credentials.api_key' => 'required|string',
            'credentials.secret_key' => 'required|string',
            'credentials.webhook_secret' => 'nullable|string',
            'supported_currencies' => 'required|array',
            'supported_currencies.*' => 'string|size:3'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();
            
            $gateway = Gateway::create([
                'user_id' => $user->id,
                'name' => $request->name,
                'type' => $request->type,
                'is_active' => $request->is_active ?? false,
                'credentials' => $request->credentials,
                'supported_currencies' => $request->supported_currencies,
                'settings' => $request->settings ?? []
            ]);

            Log::info('Gateway created', [
                'user_id' => $user->id,
                'gateway_id' => $gateway->id,
                'type' => $request->type
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Gateway created successfully',
                'data' => [
                    'id' => $gateway->id,
                    'name' => $gateway->name,
                    'type' => $gateway->type,
                    'is_active' => $gateway->is_active
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('Gateway creation failed', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Gateway creation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir les informations d'une passerelle
     */
    public function getInfo(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'gateway_id' => 'required|exists:gateways,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();
            $gateway = Gateway::where('id', $request->gateway_id)
                ->where('user_id', $user->id)
                ->first();

            if (!$gateway) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gateway not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $gateway->id,
                    'name' => $gateway->name,
                    'type' => $gateway->type,
                    'is_active' => $gateway->is_active,
                    'supported_currencies' => $gateway->supported_currencies,
                    'created_at' => $gateway->created_at->toISOString(),
                    'updated_at' => $gateway->updated_at->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Gateway info retrieval failed', [
                'user_id' => auth()->id(),
                'gateway_id' => $request->gateway_id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Gateway info retrieval failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir les devises supportées par passerelle
     */
    public function getCurrencyByGateway(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'gateway_type' => 'required|string|in:stripe,paypal,razorpay,mollie'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $currencies = $this->getSupportedCurrencies($request->gateway_type);

        return response()->json([
            'success' => true,
            'data' => [
                'gateway_type' => $request->gateway_type,
                'supported_currencies' => $currencies
            ]
        ]);
    }

    /**
     * Synchroniser les passerelles
     */
    public function syncs()
    {
        $user = auth()->user();
        $gateways = Gateway::where('user_id', $user->id)->get();
        
        return view('saas.user.gateways.syncs', compact('gateways'));
    }

    /**
     * Tester une passerelle
     */
    public function testGateway(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'gateway_id' => 'required|exists:gateways,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();
            $gateway = Gateway::where('id', $request->gateway_id)
                ->where('user_id', $user->id)
                ->first();

            if (!$gateway) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gateway not found'
                ], 404);
            }

            // Tester la connexion à la passerelle
            $testResult = $this->testGatewayConnection($gateway);

            return response()->json([
                'success' => true,
                'data' => [
                    'gateway_id' => $gateway->id,
                    'gateway_name' => $gateway->name,
                    'test_result' => $testResult
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Gateway test failed', [
                'user_id' => auth()->id(),
                'gateway_id' => $request->gateway_id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Gateway test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mettre à jour une passerelle
     */
    public function update(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'is_active' => 'sometimes|boolean',
            'credentials' => 'sometimes|array',
            'supported_currencies' => 'sometimes|array',
            'settings' => 'sometimes|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();
            $gateway = Gateway::where('id', $id)
                ->where('user_id', $user->id)
                ->first();

            if (!$gateway) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gateway not found'
                ], 404);
            }

            $gateway->update($request->only([
                'name', 'is_active', 'credentials', 
                'supported_currencies', 'settings'
            ]));

            Log::info('Gateway updated', [
                'user_id' => $user->id,
                'gateway_id' => $gateway->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Gateway updated successfully',
                'data' => [
                    'id' => $gateway->id,
                    'name' => $gateway->name,
                    'type' => $gateway->type,
                    'is_active' => $gateway->is_active
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Gateway update failed', [
                'user_id' => auth()->id(),
                'gateway_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Gateway update failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Supprimer une passerelle
     */
    public function destroy($id): JsonResponse
    {
        try {
            $user = auth()->user();
            $gateway = Gateway::where('id', $id)
                ->where('user_id', $user->id)
                ->first();

            if (!$gateway) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gateway not found'
                ], 404);
            }

            // Vérifier si la passerelle est utilisée dans des produits
            $productsUsingGateway = Product::where('user_id', $user->id)
                ->where('gateway_id', $gateway->id)
                ->count();

            if ($productsUsingGateway > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete gateway: it is being used by ' . $productsUsingGateway . ' product(s)'
                ], 400);
            }

            $gateway->delete();

            Log::info('Gateway deleted', [
                'user_id' => $user->id,
                'gateway_id' => $id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Gateway deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Gateway deletion failed', [
                'user_id' => auth()->id(),
                'gateway_id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Gateway deletion failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtenir les devises supportées par type de passerelle
     */
    private function getSupportedCurrencies(string $gatewayType): array
    {
        $currencies = [
            'stripe' => [
                'USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'SEK', 'NOK', 'DKK',
                'PLN', 'CZK', 'HUF', 'RON', 'BGN', 'HRK', 'RUB', 'TRY', 'BRL', 'MXN',
                'SGD', 'HKD', 'NZD', 'MYR', 'PHP', 'THB', 'IDR', 'INR', 'KRW', 'ZAR'
            ],
            'paypal' => [
                'USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'SEK', 'NOK', 'DKK',
                'PLN', 'CZK', 'HUF', 'RON', 'BGN', 'HRK', 'BRL', 'MXN', 'SGD', 'HKD',
                'NZD', 'MYR', 'PHP', 'THB', 'IDR', 'INR', 'KRW', 'ZAR', 'ILS', 'AED'
            ],
            'razorpay' => [
                'INR', 'USD', 'EUR', 'GBP', 'SGD', 'AED', 'AUD', 'CAD', 'CHF', 'JPY'
            ],
            'mollie' => [
                'EUR', 'USD', 'GBP', 'CAD', 'AUD', 'CHF', 'SEK', 'NOK', 'DKK', 'PLN',
                'CZK', 'HUF', 'RON', 'BGN', 'HRK', 'BRL', 'MXN', 'SGD', 'HKD', 'NZD',
                'MYR', 'PHP', 'THB', 'IDR', 'INR', 'KRW', 'ZAR', 'ILS', 'AED'
            ]
        ];

        return $currencies[$gatewayType] ?? [];
    }

    /**
     * Tester la connexion à une passerelle
     */
    private function testGatewayConnection(Gateway $gateway): array
    {
        try {
            switch ($gateway->type) {
                case 'stripe':
                    return $this->testStripeConnection($gateway);
                case 'paypal':
                    return $this->testPayPalConnection($gateway);
                case 'razorpay':
                    return $this->testRazorpayConnection($gateway);
                case 'mollie':
                    return $this->testMollieConnection($gateway);
                default:
                    return [
                        'success' => false,
                        'message' => 'Unsupported gateway type'
                    ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Tester la connexion Stripe
     */
    private function testStripeConnection(Gateway $gateway): array
    {
        // Implémentation du test Stripe
        return [
            'success' => true,
            'message' => 'Stripe connection successful',
            'account_info' => [
                'type' => 'test',
                'country' => 'US'
            ]
        ];
    }

    /**
     * Tester la connexion PayPal
     */
    private function testPayPalConnection(Gateway $gateway): array
    {
        // Implémentation du test PayPal
        return [
            'success' => true,
            'message' => 'PayPal connection successful',
            'account_info' => [
                'type' => 'business',
                'country' => 'US'
            ]
        ];
    }

    /**
     * Tester la connexion Razorpay
     */
    private function testRazorpayConnection(Gateway $gateway): array
    {
        // Implémentation du test Razorpay
        return [
            'success' => true,
            'message' => 'Razorpay connection successful',
            'account_info' => [
                'type' => 'business',
                'country' => 'IN'
            ]
        ];
    }

    /**
     * Tester la connexion Mollie
     */
    private function testMollieConnection(Gateway $gateway): array
    {
        // Implémentation du test Mollie
        return [
            'success' => true,
            'message' => 'Mollie connection successful',
            'account_info' => [
                'type' => 'business',
                'country' => 'NL'
            ]
        ];
    }
}

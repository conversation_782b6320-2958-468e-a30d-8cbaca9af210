<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ApiKey;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;

class ApiKeyController extends Controller
{
    /**
     * Créer une nouvelle clé API
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'permissions' => 'nullable|array',
            'permissions.*' => 'string|in:read,write,delete,admin,*',
            'expires_at' => 'nullable|date|after:now'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Données invalides',
                'errors' => $validator->errors()
            ], Response::HTTP_BAD_REQUEST);
        }

        $apiKey = ApiKey::create([
            'user_id' => $request->api_user->id,
            'name' => $request->name,
            'permissions' => $request->permissions ?? ['read'],
            'expires_at' => $request->expires_at,
            'is_active' => true
        ]);

        return response()->json([
            'message' => 'Clé API créée avec succès',
            'data' => [
                'id' => $apiKey->id,
                'name' => $apiKey->name,
                'key' => $apiKey->key,
                'permissions' => $apiKey->permissions,
                'expires_at' => $apiKey->expires_at,
                'created_at' => $apiKey->created_at
            ]
        ], Response::HTTP_CREATED);
    }

    /**
     * Lister toutes les clés API de l'utilisateur
     */
    public function index(Request $request)
    {
        $apiKeys = ApiKey::where('user_id', $request->api_user->id)
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($key) {
                return [
                    'id' => $key->id,
                    'name' => $key->name,
                    'key' => substr($key->key, 0, 10) . '...',
                    'permissions' => $key->permissions,
                    'last_used_at' => $key->last_used_at,
                    'expires_at' => $key->expires_at,
                    'is_active' => $key->is_active,
                    'created_at' => $key->created_at
                ];
            });

        return response()->json([
            'data' => $apiKeys
        ]);
    }

    /**
     * Afficher une clé API spécifique
     */
    public function show(Request $request, $id)
    {
        $apiKey = ApiKey::where('user_id', $request->api_user->id)
            ->where('id', $id)
            ->first();

        if (!$apiKey) {
            return response()->json([
                'error' => 'Clé API non trouvée'
            ], Response::HTTP_NOT_FOUND);
        }

        return response()->json([
            'data' => [
                'id' => $apiKey->id,
                'name' => $apiKey->name,
                'key' => $apiKey->key,
                'permissions' => $apiKey->permissions,
                'last_used_at' => $apiKey->last_used_at,
                'expires_at' => $apiKey->expires_at,
                'is_active' => $apiKey->is_active,
                'created_at' => $apiKey->created_at
            ]
        ]);
    }

    /**
     * Mettre à jour une clé API
     */
    public function update(Request $request, $id)
    {
        $apiKey = ApiKey::where('user_id', $request->api_user->id)
            ->where('id', $id)
            ->first();

        if (!$apiKey) {
            return response()->json([
                'error' => 'Clé API non trouvée'
            ], Response::HTTP_NOT_FOUND);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'permissions' => 'sometimes|nullable|array',
            'permissions.*' => 'string|in:read,write,delete,admin,*',
            'expires_at' => 'sometimes|nullable|date|after:now',
            'is_active' => 'sometimes|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Données invalides',
                'errors' => $validator->errors()
            ], Response::HTTP_BAD_REQUEST);
        }

        $apiKey->update($request->only(['name', 'permissions', 'expires_at', 'is_active']));

        return response()->json([
            'message' => 'Clé API mise à jour avec succès',
            'data' => [
                'id' => $apiKey->id,
                'name' => $apiKey->name,
                'key' => $apiKey->key,
                'permissions' => $apiKey->permissions,
                'last_used_at' => $apiKey->last_used_at,
                'expires_at' => $apiKey->expires_at,
                'is_active' => $apiKey->is_active,
                'updated_at' => $apiKey->updated_at
            ]
        ]);
    }

    /**
     * Supprimer une clé API
     */
    public function destroy(Request $request, $id)
    {
        $apiKey = ApiKey::where('user_id', $request->api_user->id)
            ->where('id', $id)
            ->first();

        if (!$apiKey) {
            return response()->json([
                'error' => 'Clé API non trouvée'
            ], Response::HTTP_NOT_FOUND);
        }

        $apiKey->delete();

        return response()->json([
            'message' => 'Clé API supprimée avec succès'
        ]);
    }

    /**
     * Régénérer une clé API
     */
    public function regenerate(Request $request, $id)
    {
        $apiKey = ApiKey::where('user_id', $request->api_user->id)
            ->where('id', $id)
            ->first();

        if (!$apiKey) {
            return response()->json([
                'error' => 'Clé API non trouvée'
            ], Response::HTTP_NOT_FOUND);
        }

        $apiKey->update(['key' => 'sk_' . \Illuminate\Support\Str::random(48)]);

        return response()->json([
            'message' => 'Clé API régénérée avec succès',
            'data' => [
                'id' => $apiKey->id,
                'name' => $apiKey->name,
                'key' => $apiKey->key,
                'updated_at' => $apiKey->updated_at
            ]
        ]);
    }
} 
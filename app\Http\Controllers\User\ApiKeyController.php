<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\ApiKey;
use App\Models\ApiKeyLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

class ApiKeyController extends Controller
{
    /**
     * Afficher la page de gestion des clés API
     */
    public function index()
    {
        $user = auth()->user();
        
        $apiKeys = ApiKey::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();
            
        // Statistiques d'utilisation
        $totalRequests = ApiKeyLog::whereIn('api_key_id', $apiKeys->pluck('id'))->count();
        $successfulRequests = ApiKeyLog::whereIn('api_key_id', $apiKeys->pluck('id'))
            ->where('status', 'success')
            ->count();
        $failedRequests = ApiKeyLog::whereIn('api_key_id', $apiKeys->pluck('id'))
            ->where('status', 'error')
            ->count();
        $lastMonthRequests = ApiKeyLog::whereIn('api_key_id', $apiKeys->pluck('id'))
            ->where('created_at', '>=', Carbon::now()->startOfMonth())
            ->count();
            
        return view('user.settings.api-keys', compact(
            'apiKeys',
            'totalRequests',
            'successfulRequests',
            'failedRequests',
            'lastMonthRequests'
        ));
    }

    /**
     * Créer une nouvelle clé API
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'permissions' => 'required|array|min:1',
            'permissions.*' => 'in:read,write,admin'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();
            
            $apiKey = ApiKey::create([
                'user_id' => $user->id,
                'name' => $request->name,
                'key' => 'pk_' . Str::random(64),
                'permissions' => $request->permissions,
                'is_active' => true,
                'last_used_at' => null
            ]);

            Log::info('API key created', [
                'user_id' => $user->id,
                'api_key_id' => $apiKey->id,
                'name' => $request->name
            ]);

            return response()->json([
                'success' => true,
                'message' => 'API key created successfully',
                'data' => [
                    'id' => $apiKey->id,
                    'name' => $apiKey->name,
                    'key' => $apiKey->key,
                    'permissions' => $apiKey->permissions
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('API key creation failed', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create API key'
            ], 500);
        }
    }

    /**
     * Obtenir les détails d'une clé API
     */
    public function show(ApiKey $apiKey): JsonResponse
    {
        $user = auth()->user();
        
        // Vérifier que l'utilisateur possède cette clé
        if ($apiKey->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Statistiques d'utilisation
        $stats = [
            'total_requests' => ApiKeyLog::where('api_key_id', $apiKey->id)->count(),
            'success_rate' => $this->calculateSuccessRate($apiKey->id),
            'this_month' => ApiKeyLog::where('api_key_id', $apiKey->id)
                ->where('created_at', '>=', Carbon::now()->startOfMonth())
                ->count()
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $apiKey->id,
                'name' => $apiKey->name,
                'permissions' => $apiKey->permissions,
                'is_active' => $apiKey->is_active,
                'created_at' => $apiKey->created_at->format('M d, Y H:i'),
                'last_used_at' => $apiKey->last_used_at ? $apiKey->last_used_at->format('M d, Y H:i') : null,
                'stats' => $stats
            ]
        ]);
    }

    /**
     * Régénérer une clé API
     */
    public function regenerate(ApiKey $apiKey): JsonResponse
    {
        $user = auth()->user();
        
        if ($apiKey->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        try {
            $oldKey = $apiKey->key;
            
            $apiKey->update([
                'key' => 'pk_' . Str::random(64),
                'last_used_at' => null
            ]);

            Log::info('API key regenerated', [
                'user_id' => $user->id,
                'api_key_id' => $apiKey->id,
                'old_key' => $oldKey
            ]);

            return response()->json([
                'success' => true,
                'message' => 'API key regenerated successfully',
                'data' => [
                    'id' => $apiKey->id,
                    'new_key' => $apiKey->key
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('API key regeneration failed', [
                'user_id' => $user->id,
                'api_key_id' => $apiKey->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to regenerate API key'
            ], 500);
        }
    }

    /**
     * Supprimer une clé API
     */
    public function destroy(ApiKey $apiKey): JsonResponse
    {
        $user = auth()->user();
        
        if ($apiKey->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        try {
            // Supprimer les logs associés
            ApiKeyLog::where('api_key_id', $apiKey->id)->delete();
            
            // Supprimer la clé
            $apiKey->delete();

            Log::info('API key deleted', [
                'user_id' => $user->id,
                'api_key_id' => $apiKey->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'API key deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('API key deletion failed', [
                'user_id' => $user->id,
                'api_key_id' => $apiKey->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete API key'
            ], 500);
        }
    }

    /**
     * Obtenir les statistiques d'utilisation
     */
    public function stats(): JsonResponse
    {
        $user = auth()->user();
        
        $apiKeys = ApiKey::where('user_id', $user->id)->pluck('id');
        
        $stats = [
            'total_keys' => $apiKeys->count(),
            'active_keys' => ApiKey::where('user_id', $user->id)->where('is_active', true)->count(),
            'total_requests' => ApiKeyLog::whereIn('api_key_id', $apiKeys)->count(),
            'successful_requests' => ApiKeyLog::whereIn('api_key_id', $apiKeys)
                ->where('status', 'success')
                ->count(),
            'failed_requests' => ApiKeyLog::whereIn('api_key_id', $apiKeys)
                ->where('status', 'error')
                ->count(),
            'this_month' => ApiKeyLog::whereIn('api_key_id', $apiKeys)
                ->where('created_at', '>=', Carbon::now()->startOfMonth())
                ->count(),
            'popular_endpoints' => ApiKeyLog::whereIn('api_key_id', $apiKeys)
                ->selectRaw('endpoint, COUNT(*) as count')
                ->groupBy('endpoint')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get()
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Calculer le taux de succès
     */
    private function calculateSuccessRate(int $apiKeyId): string
    {
        $total = ApiKeyLog::where('api_key_id', $apiKeyId)->count();
        
        if ($total === 0) {
            return '0%';
        }
        
        $successful = ApiKeyLog::where('api_key_id', $apiKeyId)
            ->where('status', 'success')
            ->count();
            
        return round(($successful / $total) * 100, 1) . '%';
    }
} 
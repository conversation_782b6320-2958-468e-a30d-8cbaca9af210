<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\ApiKey;
use App\Models\ApiKeyLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Carbon\Carbon;

class UserController extends Controller
{
    /**
     * Obtenir le profil de l'utilisateur
     */
    public function profile(Request $request): JsonResponse
    {
        $apiKey = $request->get('api_key');
        $user = $apiKey->user;
        
        return response()->json([
            'success' => true,
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'address' => $user->address,
                'city' => $user->city,
                'country' => $user->country,
                'created_at' => $user->created_at->toISOString(),
                'updated_at' => $user->updated_at->toISOString()
            ]
        ]);
    }

    /**
     * Mettre à jour le profil de l'utilisateur
     */
    public function updateProfile(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'phone' => 'sometimes|string|max:20',
            'address' => 'sometimes|string|max:500',
            'city' => 'sometimes|string|max:100',
            'country' => 'sometimes|string|max:100',
            'current_password' => 'required_with:new_password|string',
            'new_password' => 'sometimes|string|min:8|confirmed'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $apiKey = $request->get('api_key');
        $user = $apiKey->user;

        // Vérifier le mot de passe actuel si fourni
        if ($request->has('current_password')) {
            if (!Hash::check($request->current_password, $user->password)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Current password is incorrect'
                ], 422);
            }
        }

        // Mettre à jour les informations
        $user->fill($request->only(['name', 'phone', 'address', 'city', 'country']));
        
        if ($request->has('new_password')) {
            $user->password = Hash::make($request->new_password);
        }

        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'Profile updated successfully',
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'address' => $user->address,
                'city' => $user->city,
                'country' => $user->country,
                'updated_at' => $user->updated_at->toISOString()
            ]
        ]);
    }

    /**
     * Obtenir les statistiques de l'utilisateur
     */
    public function stats(Request $request): JsonResponse
    {
        $apiKey = $request->get('api_key');
        $user = $apiKey->user;

        $stats = [
            'total_products' => $user->products()->count(),
            'total_orders' => $user->orders()->count(),
            'total_revenue' => $user->orders()->where('status', 'completed')->sum('amount'),
            'active_subscriptions' => $user->subscriptions()->where('status', 'active')->count(),
            'total_customers' => $user->customers()->count(),
            'this_month_orders' => $user->orders()->where('created_at', '>=', Carbon::now()->startOfMonth())->count(),
            'this_month_revenue' => $user->orders()->where('status', 'completed')
                ->where('created_at', '>=', Carbon::now()->startOfMonth())
                ->sum('amount')
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Obtenir les commandes de l'utilisateur
     */
    public function orders(Request $request): JsonResponse
    {
        $apiKey = $request->get('api_key');
        $user = $apiKey->user;

        $orders = $user->orders()
            ->with(['product', 'customer'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $orders
        ]);
    }

    /**
     * Obtenir les détails d'une commande
     */
    public function orderDetails(Request $request, $orderId): JsonResponse
    {
        $apiKey = $request->get('api_key');
        $user = $apiKey->user;

        $order = $user->orders()
            ->with(['product', 'customer', 'subscription'])
            ->find($orderId);

        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $order
        ]);
    }

    /**
     * Lister les clés API de l'utilisateur
     */
    public function listApiKeys(Request $request): JsonResponse
    {
        $apiKey = $request->get('api_key');
        $user = $apiKey->user;

        $apiKeys = $user->apiKeys()
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($key) {
                return [
                    'id' => $key->id,
                    'name' => $key->name,
                    'permissions' => $key->permissions,
                    'is_active' => $key->is_active,
                    'last_used_at' => $key->last_used_at?->toISOString(),
                    'created_at' => $key->created_at->toISOString(),
                    'total_requests' => $key->total_requests,
                    'successful_requests' => $key->successful_requests,
                    'failed_requests' => $key->failed_requests,
                    'success_rate' => $key->success_rate
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $apiKeys
        ]);
    }

    /**
     * Créer une nouvelle clé API
     */
    public function createApiKey(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'permissions' => 'required|array|min:1',
            'permissions.*' => 'in:read,write,admin'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $apiKey = $request->get('api_key');
        $user = $apiKey->user;

        $newApiKey = ApiKey::create([
            'user_id' => $user->id,
            'name' => $request->name,
            'key' => config('api.key_prefix', 'pk_') . Str::random(config('api.key_length', 64)),
            'permissions' => $request->permissions,
            'is_active' => true,
            'last_used_at' => null
        ]);

        return response()->json([
            'success' => true,
            'message' => 'API key created successfully',
            'data' => [
                'id' => $newApiKey->id,
                'name' => $newApiKey->name,
                'key' => $newApiKey->key,
                'permissions' => $newApiKey->permissions,
                'created_at' => $newApiKey->created_at->toISOString()
            ]
        ], 201);
    }

    /**
     * Afficher les détails d'une clé API
     */
    public function showApiKey(Request $request, ApiKey $apiKey): JsonResponse
    {
        $currentApiKey = $request->get('api_key');
        $user = $currentApiKey->user;

        // Vérifier que la clé appartient à l'utilisateur
        if ($apiKey->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'API key not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $apiKey->id,
                'name' => $apiKey->name,
                'permissions' => $apiKey->permissions,
                'is_active' => $apiKey->is_active,
                'last_used_at' => $apiKey->last_used_at?->toISOString(),
                'created_at' => $apiKey->created_at->toISOString(),
                'total_requests' => $apiKey->total_requests,
                'successful_requests' => $apiKey->successful_requests,
                'failed_requests' => $apiKey->failed_requests,
                'success_rate' => $apiKey->success_rate,
                'this_month_requests' => $apiKey->this_month_requests,
                'today_requests' => $apiKey->today_requests
            ]
        ]);
    }

    /**
     * Régénérer une clé API
     */
    public function regenerateApiKey(Request $request, ApiKey $apiKey): JsonResponse
    {
        $currentApiKey = $request->get('api_key');
        $user = $currentApiKey->user;

        // Vérifier que la clé appartient à l'utilisateur
        if ($apiKey->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'API key not found'
            ], 404);
        }

        $oldKey = $apiKey->key;
        $newKey = config('api.key_prefix', 'pk_') . Str::random(config('api.key_length', 64));

        $apiKey->update([
            'key' => $newKey,
            'last_used_at' => null
        ]);

        return response()->json([
            'success' => true,
            'message' => 'API key regenerated successfully',
            'data' => [
                'id' => $apiKey->id,
                'name' => $apiKey->name,
                'old_key' => $oldKey,
                'new_key' => $newKey,
                'regenerated_at' => now()->toISOString()
            ]
        ]);
    }

    /**
     * Supprimer une clé API
     */
    public function deleteApiKey(Request $request, ApiKey $apiKey): JsonResponse
    {
        $currentApiKey = $request->get('api_key');
        $user = $currentApiKey->user;

        // Vérifier que la clé appartient à l'utilisateur
        if ($apiKey->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'API key not found'
            ], 404);
        }

        $apiKey->delete();

        return response()->json([
            'success' => true,
            'message' => 'API key deleted successfully'
        ]);
    }

    /**
     * Obtenir les statistiques des clés API
     */
    public function apiKeyStats(Request $request): JsonResponse
    {
        $apiKey = $request->get('api_key');
        $user = $apiKey->user;

        $userApiKeys = $user->apiKeys();
        
        $stats = [
            'total_keys' => $userApiKeys->count(),
            'active_keys' => $userApiKeys->where('is_active', true)->count(),
            'inactive_keys' => $userApiKeys->where('is_active', false)->count(),
            'total_requests' => ApiKeyLog::whereIn('api_key_id', $userApiKeys->pluck('id'))->count(),
            'successful_requests' => ApiKeyLog::whereIn('api_key_id', $userApiKeys->pluck('id'))
                ->where('status', 'success')->count(),
            'failed_requests' => ApiKeyLog::whereIn('api_key_id', $userApiKeys->pluck('id'))
                ->where('status', 'error')->count(),
            'this_month_requests' => ApiKeyLog::whereIn('api_key_id', $userApiKeys->pluck('id'))
                ->where('created_at', '>=', Carbon::now()->startOfMonth())->count(),
            'today_requests' => ApiKeyLog::whereIn('api_key_id', $userApiKeys->pluck('id'))
                ->where('created_at', '>=', Carbon::now()->startOfDay())->count()
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
} 